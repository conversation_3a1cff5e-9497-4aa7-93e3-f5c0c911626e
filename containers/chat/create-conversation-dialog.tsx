import { listChatChannel } from '@/apis/chat_v1/api';
import { ChatChannelDTO } from '@/apis/chat_v1/models';
import { createCsConversation } from '@/apis/cs_chat_v1/api';
import { CreateCsConversationRequest } from '@/apis/cs_chat_v1/models';
import { getCustomer, listCustomers } from '@/apis/customer_v1/api';
import { CustomerDTO } from '@/apis/customer_v1/models';
import MetaForm from '@/components/form/metaform';
import { FormItem } from '@/components/form/metaform/types';
import SelectRemote from '@/components/input/select-remote';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import alert from '@/lib/alert';
import { handleResponse, Page, Sort } from '@/lib/api';
import useTranslation from 'next-translate/useTranslation';
import { z } from 'zod';

const createConversationSchema = (t: any) => z.object({
    customer_id: z.string().min(1, t('customer is required')),
    chat_channel_id: z.string().min(1, t('channel is required')),
});

interface CreateConversationDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSuccess?: () => void;
}

const CreateConversationDialog = ({ open, onOpenChange, onSuccess }: CreateConversationDialogProps) => {
    const { t: tChat } = useTranslation('chat');
    const { t: tCommon } = useTranslation('common');

    const handleSubmit = async (data: z.infer<ReturnType<typeof createConversationSchema>>) => {
        try {
            const request: CreateCsConversationRequest = {
                customer_id: parseInt(data.customer_id),
                chat_channel_id: parseInt(data.chat_channel_id),
            };
            const resp = await createCsConversation(request);
            if (resp.data?.success) {
                alert.successSave();
                onOpenChange(false);
                onSuccess?.();
            }
        } catch (error: any) {
            handleResponse(error.response);
        }
    };

    const createConversationFormItems = (): FormItem<z.infer<ReturnType<typeof createConversationSchema>>>[] => [
        {
            name: 'customer_id',
            label: tChat('customer'),
            required: true,
            render: (field) => (
                <SelectRemote<CustomerDTO>
                    value={field.value}
                    onChange={(value, item) => field.onChange(value)}
                    onList={(page: Page, sort: Sort, keyword: string) =>
                        listCustomers(page.page, page.size, sort.field, sort.asc, keyword, true, '')
                    }
                    onGet={(id: string) => getCustomer(id)}
                    renderLabel={(item: CustomerDTO) =>
                        item.display_name || `${item.first_name || ''} ${item.last_name || ''}`.trim() || tChat('unknown customer')
                    }
                    valueKey="id"
                    placeholder={tChat('select customer')}
                />
            ),
        },
        {
            name: 'chat_channel_id',
            label: tChat('channel'),
            required: true,
            render: (field) => (
                <SelectRemote<ChatChannelDTO>
                    value={field.value}
                    onChange={(value, item) => field.onChange(value)}
                    onList={(page: Page, sort: Sort, keyword: string) =>
                        listChatChannel(page.page, page.size, sort.field, sort.asc, keyword, '', true, false)
                    }
                    renderLabel={(item: ChatChannelDTO) =>
                        `${item.platform} - ${item.display_phone_number}`
                    }
                    valueKey="id"
                    placeholder={tChat('select channel')}
                />
            ),
        },
    ];

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>{tChat('create conversation')}</DialogTitle>
                </DialogHeader>
                <MetaForm
                    schema={createConversationSchema(tChat)}
                    items={createConversationFormItems()}
                    defaultValues={{
                        customer_id: '',
                        chat_channel_id: '',
                    }}
                    saveButton
                    saveButtonText={tCommon('create')}
                    onCancel={() => onOpenChange(false)}
                    onSubmit={handleSubmit}
                />
            </DialogContent>
        </Dialog>
    );
};

export default CreateConversationDialog;