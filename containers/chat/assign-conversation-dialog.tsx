import React from 'react';
import { getWorkspaceU<PERSON>, getWorkspaceUserList } from '@/apis/workspace_v1/api';
import { updateCsConversation } from '@/apis/cs_chat_v1/api';
import { WorkspaceUserDTO } from '@/apis/workspace_v1/models';
import { CsConversationDTO } from '@/apis/cs_chat_v1/models';
import SelectRemote from '@/components/input/select-remote';
import { Button } from '@/components/ui/button';
import CustomDialog from '@/components/display/custom-dialog';
import alert from '@/lib/alert';
import { handleResponse, Page, Sort } from '@/lib/api';
import { useState } from '@/lib/react';
import useTranslation from 'next-translate/useTranslation';

interface AssignConversationDialogProps {
    open: boolean;
    onClose: () => void;
    conversation: CsConversationDTO | null;
    onSuccess: () => void;
}

const AssignConversationDialog = ({ open, onClose, conversation, onSuccess }: AssignConversationDialogProps) => {
    const { t: tChat } = useTranslation('chat');
    const { t: tCommon } = useTranslation('common');
    const selectedUserId = useState<string>('');
    const isLoading = useState<boolean>(false);

    // Reset selected user when dialog opens or conversation changes
    React.useEffect(() => {
        if (open && conversation) {
            selectedUserId.set(conversation.customer_service?.id?.toString() || '');
        }
    }, [open, conversation?.id, conversation?.customer_service?.id]);

    // Reset state when dialog closes
    React.useEffect(() => {
        if (!open) {
            selectedUserId.set('');
            isLoading.set(false);
        }
    }, [open]);

    const handleAssign = async () => {
        if (!conversation || !selectedUserId.value || isLoading.value) {
            return;
        }

        isLoading.set(true);
        try {
            const resp = await updateCsConversation(conversation.id, {
                customer_service_id: parseInt(selectedUserId.value)
            });

            if (resp.data?.success) {
                alert.successSave();
                onClose();
                onSuccess();
            }
        } catch (error: any) {
            handleResponse(error.response);
        } finally {
            isLoading.set(false);
        }
    };

    const getCustomerName = (conversation: CsConversationDTO): string => {
        if (conversation.customer?.display_name) {
            return conversation.customer.display_name;
        }
        if (conversation.customer?.first_name || conversation.customer?.last_name) {
            return `${conversation.customer?.first_name || ''} ${conversation.customer?.last_name || ''}`.trim();
        }
        return 'Unknown Customer';
    };

    return (
        <CustomDialog
            open={open}
            onClose={onClose}
            title={tChat('assign conversation')}
            className="max-w-lg"
        >
            {conversation && (
                <div className="space-y-4">
                    {/* Conversation Info */}
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                {conversation.customer?.avatar ? (
                                    <img
                                        src={conversation.customer.avatar}
                                        alt={getCustomerName(conversation)}
                                        className="w-full h-full rounded-full object-cover"
                                    />
                                ) : (
                                    getCustomerName(conversation).split(' ').map(n => n[0]).join('').toUpperCase()
                                )}
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900 dark:text-white">
                                    {getCustomerName(conversation)}
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {tChat('conversation')} #{conversation.id}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Assignee Selection */}
                    <div className="space-y-2 relative">
                        <label className="text-sm font-medium text-gray-900 dark:text-white">
                            {tChat('assign to')}
                        </label>
                        <div className="relative z-10">
                            <SelectRemote<WorkspaceUserDTO>
                                value={selectedUserId.value}
                                onChange={(value) => selectedUserId.set(value)}
                                onList={(page: Page, sort: Sort, keyword: string) =>
                                    getWorkspaceUserList(page.page, page.size, sort.field, sort.asc, keyword)
                                }
                                onGet={(id: string) => getWorkspaceUser(id)}
                                renderLabel={(item: WorkspaceUserDTO) =>
                                    item.user?.display_name || item.user?.username || item?.user?.invited_email || tChat('unknown user')
                                }
                                valueKey="id"
                                placeholder={tChat('select assignee')}
                                disabled={isLoading.value}
                            />
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-2 pt-4">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            disabled={isLoading.value}
                            type="button"
                        >
                            {tCommon('cancel')}
                        </Button>
                        <Button
                            onClick={handleAssign}
                            disabled={!selectedUserId.value || isLoading.value}
                            type="button"
                        >
                            {isLoading.value ? tCommon('saving') : tChat('assign')}
                        </Button>
                    </div>
                </div>
            )}
        </CustomDialog>
    );
};

export default AssignConversationDialog;
