import { getCsConversation, sendCsMessage, deleteCsConversation } from "@/apis/cs_chat_v1/api";
import { CsConversationDTO, SendMessageRequest } from "@/apis/cs_chat_v1/models";
import { Button } from "@/components/ui/button";
import { useVoiceRecorder, VoiceRecordingData } from "@/hooks/useVoiceRecorder";
import { handleResponse } from "@/lib/api";
import { useState } from "@/lib/react";
import alert from "@/lib/alert";
import { Mic, Play, Send } from "lucide-react";
import { useRouter } from "next/router";
import { useEffect, useRef } from "react";
import ChatHeader from "./chat-header";
import ChatSidebar from "./chat-sidebar";
import CustomerInfoPanel from "./customer-info-panel";
import { useCsMessageWebSocket } from "./hooks/use-cs-message-websocket";
import MessageInput from "./message-input";
import MessageList, { MessageListRef } from "./message-list";
import AssignConversationDialog from "./assign-conversation-dialog";
import { useDeleteModal } from "@/contexts/delete.context";
import useTranslation from "next-translate/useTranslation";

// Helper function to convert CsConversationDTO to header format
const getConversationForHeader = (csConversation: CsConversationDTO, tChat: (key: string) => string) => {
    const customerName = csConversation.customer?.display_name ||
        `${csConversation.customer?.first_name || ''} ${csConversation.customer?.last_name || ''}`.trim() ||
        tChat('unknown customer');

    return {
        id: csConversation.id.toString(),
        name: customerName,
        avatar: csConversation.customer?.avatar,
        isOnline: false, // We don't track online status for customers
        lastSeen: undefined,
        participantCount: undefined,
        isGroup: false,
    };
};

const ChatLayout = () => {
    const { t: tChat } = useTranslation('chat');
    const selectedConversationId = useState<string | undefined>(undefined);
    const isLoading = useState<boolean>(false);
    const isMobileSidebarOpen = useState<boolean>(false);
    const selectedCsConversation = useState<CsConversationDTO | undefined>(undefined);
    const isCustomerInfoOpen = useState<boolean>(false);
    const pendingVoiceRecording = useState<VoiceRecordingData | null>(null);
    const isAssignDialogOpen = useState<boolean>(false);
    const conversationToAssign = useState<CsConversationDTO | null>(null);
    const messageListRef = useRef<MessageListRef>(null);
    const router = useRouter();
    const msgWebSocket = useCsMessageWebSocket(selectedCsConversation.value?.id);
    const deleteModal = useDeleteModal();

    const selectedConversation = selectedCsConversation.value ? getConversationForHeader(selectedCsConversation.value, tChat) : undefined;

    const handleConversationSelect = (conversationId: string, conversation: CsConversationDTO) => {
        router.push(`/app/chat/chat?conversation_id=${conversationId}`);
        // Close mobile sidebar when conversation is selected
        isMobileSidebarOpen.set(false);
        // Optionally set the conversation directly to avoid extra API call
        selectedCsConversation.set(conversation);
    };

    useEffect(() => {
        const conversationId = router.query.conversation_id;
        if (conversationId) {
            selectedConversationId.set(conversationId as string);
            fetchConversation(conversationId as string);
        }
    }, [router.query.conversation_id]);

    const fetchConversation = async (conversationId: string) => {
        try {
            const resp = await getCsConversation(conversationId);
            if (resp.data?.success) {
                selectedCsConversation.set(resp.data.data);
            }
        } catch (error: any) {
            handleResponse(error.response)
        }
    }

    const handleSendMessage = async (messageContent: string, files?: any[]) => {
        if (!selectedCsConversation.value?.id) return;

        isLoading.set(true);

        try {
            let sendMessageRequest: SendMessageRequest;

            if (files && files.length > 0) {
                // Handle file message - for now, we'll send the first file
                const file = files[0];
                const reader = new FileReader();

                reader.onload = async () => {
                    const base64Content = (reader.result as string).split(',')[1];

                    sendMessageRequest = {
                        conversation_id: selectedCsConversation.value?.id || 0,
                        message_type: file.type === 'image' ? 'image' : file.type === 'video' ? 'video' : file.type === 'audio' ? 'voice' : 'file',
                        text: messageContent || undefined,
                        filename: file.file.name,
                        media_content: base64Content
                    };

                    try {
                        const resp = await sendCsMessage(sendMessageRequest);
                        if (resp.data?.success) {
                            // Force immediate refresh
                            msgWebSocket.forceRefresh();

                            // Also trigger a regular refresh as backup
                            setTimeout(() => {
                                msgWebSocket.refresh();
                            }, 100);

                            // Scroll to bottom after a short delay to allow message to load
                            setTimeout(() => {
                                messageListRef.current?.scrollToBottom();
                            }, 200);
                        }
                    } catch (error: any) {
                        handleResponse(error.response)
                    }
                    isLoading.set(false);
                };

                reader.readAsDataURL(file.file);
            } else {
                // Handle text message
                sendMessageRequest = {
                    conversation_id: selectedCsConversation.value.id,
                    message_type: "text",
                    text: messageContent
                };

                const resp = await sendCsMessage(sendMessageRequest);
                if (resp.data?.success) {
                    // Force immediate refresh
                    msgWebSocket.forceRefresh();

                    // Also trigger a regular refresh as backup
                    setTimeout(() => {
                        msgWebSocket.refresh();
                    }, 100);

                    // Scroll to bottom after a short delay to allow message to load
                    setTimeout(() => {
                        messageListRef.current?.scrollToBottom();
                    }, 200);
                }
                isLoading.set(false);
            }
        } catch (error: any) {
            handleResponse(error.response)
            isLoading.set(false);
        }
    };

    // Voice recording functionality
    const handleVoiceRecordingComplete = async (data: VoiceRecordingData) => {
        // Set pending recording for preview
        pendingVoiceRecording.set(data);
    };

    const handleSendVoiceMessage = async () => {
        if (!selectedCsConversation.value?.id || !pendingVoiceRecording.value) return;

        const data = pendingVoiceRecording.value;

        // Send voice message with base64 content
        const sendMessageRequest: SendMessageRequest = {
            conversation_id: selectedCsConversation.value.id,
            message_type: "voice",
            media_content: data.audioBase64,
            filename: `voice_message_${Date.now()}.webm`
        };

        try {
            isLoading.set(true);
            const resp = await sendCsMessage(sendMessageRequest);
            if (resp.data?.success) {
                // Force immediate refresh
                msgWebSocket.forceRefresh();

                // Also trigger a regular refresh as backup
                setTimeout(() => {
                    msgWebSocket.refresh();
                }, 100);

                // Scroll to bottom after a short delay to allow message to load
                setTimeout(() => {
                    messageListRef.current?.scrollToBottom();
                }, 200);

                pendingVoiceRecording.set(null); // Clear pending recording
            }
        } catch (error: any) {
            handleResponse(error.response);
        } finally {
            isLoading.set(false);
        }
    };

    const handleDiscardVoiceMessage = () => {
        if (pendingVoiceRecording.value?.audioUrl) {
            URL.revokeObjectURL(pendingVoiceRecording.value.audioUrl);
        }
        pendingVoiceRecording.set(null);
    };

    const {
        isRecording,
        error: voiceError,
        startRecording,
        stopRecording,
        cancelRecording,
        clearError: clearVoiceError
    } = useVoiceRecorder(handleVoiceRecordingComplete);

    const handleVoiceCall = () => {
        console.log("Initiating voice call...");
    };

    const handleVideoCall = () => {
        console.log("Initiating video call...");
    };

    const handleMoreOptions = () => {
        console.log("Opening more options...");
    };

    const handleAttachFile = () => {
        console.log("Opening file picker...");
    };

    const handleVoiceMessage = () => {
        startRecording();
    };

    const handleStopRecording = () => {
        stopRecording();
    };

    const handleToggleEmoji = () => {
        console.log("Toggling emoji picker...");
    };

    const handleBackToConversations = () => {
        // On mobile, just open the sidebar to show conversation list
        // Don't clear the selected conversation
        isMobileSidebarOpen.set(true);
    };

    const toggleMobileSidebar = () => {
        isMobileSidebarOpen.set(!isMobileSidebarOpen.value);
    };

    const toggleCustomerInfo = () => {
        isCustomerInfoOpen.set(!isCustomerInfoOpen.value);
    };

    const handleAssignConversation = (conversation: CsConversationDTO) => {
        conversationToAssign.set(conversation);
        isAssignDialogOpen.set(true);
    };

    // Handler for assigning conversation from header
    const handleAssignCurrentConversation = () => {
        if (selectedCsConversation.value) {
            handleAssignConversation(selectedCsConversation.value);
        }
    };

    // Handler for deleting conversation from header
    const handleDeleteCurrentConversation = () => {
        if (selectedCsConversation.value) {
            deleteModal.openDeleteModal(async () => {
                try {
                    const resp = await deleteCsConversation(selectedCsConversation.value!.id);
                    if (resp.data?.success) {
                        alert.successDelete();
                        // Navigate back to chat list after successful deletion
                        router.push('/app/chat/chat');
                        selectedConversationId.set(undefined);
                        selectedCsConversation.set(undefined);
                    }
                } catch (error: any) {
                    handleResponse(error.response);
                }
            });
        }
    };

    return (
        <div className="flex h-full bg-gray-50 dark:bg-gray-900 rounded-lg overflow-hidden mx-[2px] relative">
            {/* Mobile Overlay for Sidebar */}
            {isMobileSidebarOpen.value && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                    onClick={() => isMobileSidebarOpen.set(false)}
                />
            )}

            {/* Mobile Overlay for Customer Info */}
            {isCustomerInfoOpen.value && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                    onClick={() => isCustomerInfoOpen.set(false)}
                />
            )}

            {/* Left Sidebar */}
            <div className={`
                ${isMobileSidebarOpen.value ? 'translate-x-0' : '-translate-x-full'}
                md:translate-x-0 
                fixed md:relative 
                z-50 md:z-auto
                transition-transform duration-300 ease-in-out
                h-full
            `}>
                <ChatSidebar
                    selectedConversationId={selectedConversationId.value}
                    onConversationSelect={handleConversationSelect}
                    onAssignConversation={handleAssignConversation}
                />
            </div>

            {/* Right Content Area */}
            <div className="flex-1 flex flex-col w-full md:w-auto">
                {/* Chat Header */}
                <ChatHeader
                    conversation={selectedConversation}
                    csConversation={selectedCsConversation.value}
                    onVoiceCall={handleVoiceCall}
                    onVideoCall={handleVideoCall}
                    onMoreOptions={handleMoreOptions}
                    onBackClick={handleBackToConversations}
                    onMenuClick={toggleMobileSidebar}
                    onCustomerInfoToggle={toggleCustomerInfo}
                    isCustomerInfoOpen={isCustomerInfoOpen.value}
                    onAssignConversation={handleAssignCurrentConversation}
                    onDeleteConversation={handleDeleteCurrentConversation}
                />

                {/* Messages Area */}
                <div className="flex flex-1 overflow-hidden">
                    {/* Main Chat Area */}
                    <div className="flex-1 flex flex-col">
                        {selectedConversation ? (
                            <>
                                <MessageList
                                    ref={messageListRef}
                                    conversationId={selectedCsConversation.value?.id}
                                />
                                {/* Voice Recording Preview */}
                                {pendingVoiceRecording.value && (
                                    <div className="mx-4 md:mx-6 mb-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-blue-200 dark:border-blue-800/50 rounded-xl p-3 md:p-4 shadow-sm">
                                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                            <div className="flex items-center space-x-3 md:space-x-4 min-w-0 flex-1">
                                                <div className="w-10 h-10 md:w-12 md:h-12 bg-blue-100 dark:bg-blue-900/40 rounded-xl flex items-center justify-center shadow-sm shrink-0">
                                                    <Mic className="h-4 w-4 md:h-5 md:w-5 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-semibold text-blue-900 dark:text-blue-100 truncate">
                                                        Audio message recorded
                                                    </p>
                                                    <div className="flex items-center space-x-2 mt-1">
                                                        <p className="text-xs text-blue-700 dark:text-blue-300 font-medium">
                                                            Duration: {Math.floor(pendingVoiceRecording.value.duration / 60)}:{(pendingVoiceRecording.value.duration % 60).toString().padStart(2, '0')}
                                                        </p>
                                                        <span className="text-xs text-blue-500 dark:text-blue-400 hidden sm:inline">•</span>
                                                        <p className="text-xs text-blue-600 dark:text-blue-400 hidden sm:block">
                                                            Ready to send
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center justify-end space-x-2 shrink-0">
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => {
                                                        try {
                                                            const audio = new Audio(pendingVoiceRecording.value!.audioUrl);
                                                            audio.play().catch((error) => {
                                                                console.error('Preview audio playback error:', error);
                                                                // Fallback: try with data URL format
                                                                const fallbackAudio = new Audio(`data:${pendingVoiceRecording.value!.mimeType};base64,${pendingVoiceRecording.value!.audioBase64}`);
                                                                fallbackAudio.play().catch(console.error);
                                                            });
                                                        } catch (error) {
                                                            console.error('Error creating audio preview:', error);
                                                        }
                                                    }}
                                                    className="h-9 w-9 text-blue-700 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-200 dark:hover:bg-blue-900/30 rounded-lg font-medium transition-all"
                                                >
                                                    <Play className="h-3 w-3" />
                                                </Button>
                                                <Button
                                                    size="icon"
                                                    onClick={handleSendVoiceMessage}
                                                    disabled={isLoading.value}
                                                    className="h-9 w-9 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm hover:shadow-md font-medium transition-all disabled:opacity-50"
                                                >
                                                    <Send className="h-3 w-3" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    onClick={handleDiscardVoiceMessage}
                                                    className="h-9 px-3 md:px-4 text-gray-600 hover:text-gray-700 border-gray-300 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-800 rounded-lg font-medium transition-all text-sm"
                                                >
                                                    Discard
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                <MessageInput
                                    onSendMessage={handleSendMessage}
                                    onAttachFile={handleAttachFile}
                                    onToggleEmoji={handleToggleEmoji}
                                    onVoiceMessage={handleVoiceMessage}
                                    onStopRecording={handleStopRecording}
                                    onCancelRecording={cancelRecording}
                                    isRecording={isRecording}
                                    disabled={isLoading.value || isRecording || !!pendingVoiceRecording.value}
                                />
                            </>
                        ) : (
                            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                                <div className="text-center px-4">
                                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                        Welcome to Chat
                                    </h3>
                                    <p className="text-gray-500 dark:text-gray-400 mb-4 text-sm md:text-base">
                                        <span className="hidden md:inline">Select a conversation from the sidebar to start chatting</span>
                                        <span className="md:hidden">Tap the menu to view conversations</span>
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Customer Info Panel */}
                    {selectedCsConversation.value?.customer && isCustomerInfoOpen.value && (
                        <CustomerInfoPanel
                            customer={selectedCsConversation.value.customer}
                            isOpen={isCustomerInfoOpen.value}
                            onClose={() => isCustomerInfoOpen.set(false)}
                        />
                    )}
                </div>
            </div>

            {/* Assign Conversation Dialog */}
            <AssignConversationDialog
                open={isAssignDialogOpen.value}
                onClose={() => {
                    isAssignDialogOpen.set(false);
                    conversationToAssign.set(null);
                }}
                conversation={conversationToAssign.value}
                onSuccess={() => {
                    // Dialog will close automatically, just reset state
                    isAssignDialogOpen.set(false);
                    conversationToAssign.set(null);
                }}
            />
        </div>
    );
};

export default ChatLayout;