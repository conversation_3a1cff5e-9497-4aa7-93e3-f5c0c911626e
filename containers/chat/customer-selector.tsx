import { listCustomers } from '@/apis/customer_v1/api';
import { CustomerDTO, CustomerTagDTO } from '@/apis/customer_v1/models';
import Avatar from '@/components/display/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TagsManager } from '@/containers/relationship/tags-manager';
import { useState } from '@/lib/react';
import useTranslation from 'next-translate/useTranslation';
import { useEffect, useMemo } from 'react';
import MetaTableAdvance from '../../components/table/metatable-advance';
import useListHelper from '../../components/table/metatable-advance/list-helper';

interface CustomerSelectorProps {
    selectedItems: CustomerDTO[];
    onSelectionChange: (selectedItems: CustomerDTO[]) => void;
}

const CustomerSelector = ({ selectedItems, onSelectionChange }: CustomerSelectorProps) => {
    const { t: tChat } = useTranslation('chat');
    const { t: tRelationship } = useTranslation('relationship');
    const helper = useListHelper<CustomerDTO>();
    const filterTags = useState<CustomerTagDTO[]>([]);

    useEffect(() => {
        if (selectedItems?.length > 0) {
            helper.selectedItems.set(selectedItems)
        }
    }, [selectedItems])

    const columns = useMemo(() => [
        {
            title: '',
            dataIndex: 'avatar',
            render: (_: any, record: CustomerDTO) => (
                <Avatar
                    base64={record.avatar}
                    text={record.display_name || `${record.first_name || ''} ${record.last_name || ''}`.trim()}
                    className="w-8 h-8"
                />
            )
        },
        {
            title: tRelationship('display name'),
            dataIndex: 'display_name',
            render: (_: any, record: CustomerDTO) => record.display_name || record.company_name || `${record.first_name || ''} ${record.last_name || ''}`.trim() || tChat('unknown customer')
        },
        {
            title: tRelationship('tags'),
            dataIndex: 'tags',
            render: (_: any, record: CustomerDTO) => <TagsManager tags={record.tags} readonly={true} maxDisplayCount={5} />
        },
        {
            title: tRelationship('phone number'),
            dataIndex: 'phone_number',
            render: (_: any, record: CustomerDTO) => {
                if (!record.phone_code && !record.phone) return '-';
                return `+${record.phone_code} ${record.phone}`;
            }
        }
    ], [tRelationship]);

    const fetchCustomers = async (page: number, size: number, field: string, asc: boolean, keyword: string, filterTagIds?: string) => {
        try {
            const response = await listCustomers(page, size, field, asc, keyword, true, filterTagIds || '');
            return response;
        } catch (error) {
            console.error('Error fetching customers:', error);
            throw error;
        }
    };

    return (
        <Card>
            <CardContent>
                <div className="mb-theme-16 p-4">
                    <div className="mb-2 text-theme-base font-semibold text-muted-foreground mb-theme-4">{tRelationship('filter customers by tags')}</div>
                    <TagsManager tags={filterTags.value} onChange={(tags) => filterTags.set(tags)} />
                </div>
                <MetaTableAdvance<CustomerDTO>
                    columns={columns}
                    helper={helper}
                    onList={(p: any, s: any, k: any) => fetchCustomers(p.page, p.size, s.field, s.asc, k, filterTags.value.map((tag) => tag.id).join(','))}
                    onListRef={[filterTags.value]}
                    selectMode
                    onSelect={onSelectionChange}
                    selectKey="id"
                    minWidth="100%"
                />
            </CardContent>
        </Card>
    );
};

export default CustomerSelector; 