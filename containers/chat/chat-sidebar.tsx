import { CsConversationDTO } from "@/apis/cs_chat_v1/models";
import { deleteCsConversation, updateCsConversation } from "@/apis/cs_chat_v1/api";
import Pagination from "@/components/table/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useState } from "@/lib/react";
import { Loader2, MoreVertical, Pin, PinOff, Plus, Search, Trash2, Mic, UserPlus, UserCheck } from "lucide-react";
import { useCsConversationWebSocket } from "./hooks/use-cs-conversation-websocket";
import { handleResponse } from "@/lib/api";
import alert from "@/lib/alert";
import CreateConversationDialog from "./create-conversation-dialog";
import FaIcon from "@/containers/layout/fa-icon";
import useTranslation from "next-translate/useTranslation";
import { useDeleteModal } from "@/contexts/delete.context";

// Filter types for conversations
type ChatFilter = 'my-chats' | 'unread' | 'supervised' | 'unsupervised' | 'chats-all' | 'chats-closed';

interface FilterOption {
    key: ChatFilter;
    labelKey: string;
    descriptionKey: string;
}

interface IProps {
    selectedConversationId?: string;
    onConversationSelect: (conversationId: string, conversation: CsConversationDTO) => void;
    onAssignConversation: (conversation: CsConversationDTO) => void;
}

// Map frontend filter keys to backend constants
const getBackendFilter = (frontendFilter: ChatFilter): string => {
    switch (frontendFilter) {
        case 'my-chats':
            return 'my_chats';
        case 'unread':
            return 'unread';
        case 'supervised':
            return 'supervised';
        case 'unsupervised':
            return 'non_supervised';
        case 'chats-all':
            return 'all_chats';
        case 'chats-closed':
            return 'closed';
        default:
            return 'all_chats';
    }
};

const ChatSidebar = ({ selectedConversationId, onConversationSelect, onAssignConversation }: IProps) => {
    const { t: tChat } = useTranslation('chat');
    const searchQuery = useState<string>("");
    const currentPage = useState<number>(1);
    const activeFilter = useState<ChatFilter>('chats-all');
    const pageSize = 10;
    const isCreateDialogOpen = useState<boolean>(false);

    const deleteModal = useDeleteModal()

    // Define filter options with translation keys
    const FILTER_OPTIONS: FilterOption[] = [
        { key: 'my-chats', labelKey: 'my chats', descriptionKey: 'conversations assigned to me' },
        { key: 'unread', labelKey: 'unread', descriptionKey: 'conversations with unread messages' },
        { key: 'supervised', labelKey: 'supervised', descriptionKey: 'conversations under supervision' },
        { key: 'unsupervised', labelKey: 'unsupervised', descriptionKey: 'conversations not under supervision' },
        { key: 'chats-all', labelKey: 'chats all', descriptionKey: 'all active conversations' },
        { key: 'chats-closed', labelKey: 'chats closed', descriptionKey: 'completed conversations' },
    ];

    // Determine WebSocket parameters based on active filter
    const getWebSocketParams = () => {
        const baseParams = {
            page: currentPage.value,
            size: pageSize,
            filter: getBackendFilter(activeFilter.value),
        };

        return baseParams;
    };

    // WebSocket connection for real-time conversation list
    const {
        conversations,
        page: wsPage,
        isConnected,
        isLoading,
        isRefreshing,
        error,
        sendRequest,
        sendMessage,
        refresh
    } = useCsConversationWebSocket(getWebSocketParams());

    // Helper function to check if conversation has unread messages
    const hasUnreadMessages = (conversation: CsConversationDTO): boolean => {
        // Check if there's a customer last message
        const customerLastMessage = conversation.customer_last_message?.message;
        if (!customerLastMessage?.timestamp) return false;

        // If conversation is closed, it's not unread
        if (conversation.closed_at > 0) return false;

        // Get the last message from customer service agent
        const lastMessage = conversation.last_message?.message;
        const lastAgentMessage = lastMessage && lastMessage.platform_sender_id !== customerLastMessage.platform_sender_id ? lastMessage : null;

        // If there's no agent response, or customer message is newer than agent message, it's unread
        if (!lastAgentMessage) return true;

        return customerLastMessage.timestamp > lastAgentMessage.timestamp;
    };

    // Since filtering is now handled by the backend, we only need to filter by search query
    const filteredConversations = conversations.filter(conversation => {
        const customerName = conversation.customer?.display_name || conversation.customer?.first_name + ' ' + conversation.customer?.last_name || 'Unknown';
        const lastMessage = conversation.last_message?.message?.text || conversation.customer_last_message?.message?.text || '';
        const searchTerm = searchQuery.value.toLowerCase();

        // Apply search filter only - backend handles the filter logic
        return customerName.toLowerCase().includes(searchTerm) ||
            lastMessage.toLowerCase().includes(searchTerm);
    });

    // Handle filter change
    const handleFilterChange = (filter: ChatFilter) => {
        activeFilter.set(filter);
        currentPage.set(1); // Reset to first page when filter changes

        // Send new filter parameters to WebSocket
        sendRequest({
            page: 1,
            size: pageSize,
            filter: getBackendFilter(filter),
        });
    };

    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    };

    const formatTimestamp = (timestamp?: number) => {
        if (!timestamp) return '';

        const date = new Date(timestamp * 1000); // Convert from seconds to milliseconds
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

        if (diffInMinutes < 1) return tChat('just now');
        if (diffInMinutes < 60) return `${diffInMinutes} ${tChat('min ago')}`;
        if (diffInMinutes < 1440) {
            const hours = Math.floor(diffInMinutes / 60);
            return `${hours} ${hours > 1 ? tChat('hours ago') : tChat('hour ago')}`;
        }
        if (diffInMinutes < 10080) {
            const days = Math.floor(diffInMinutes / 1440);
            return `${days} ${days > 1 ? tChat('days ago') : tChat('day ago')}`;
        }

        return date.toLocaleDateString();
    };

    const getCustomerName = (conversation: CsConversationDTO): string => {
        if (conversation.customer?.display_name) {
            return conversation.customer.display_name;
        }
        if (conversation.customer?.first_name || conversation.customer?.last_name) {
            return `${conversation.customer?.first_name || ''} ${conversation.customer?.last_name || ''}`.trim();
        }
        return tChat('unknown customer');
    };

    const formatDuration = (seconds: number) => {
        if (!isFinite(seconds) || seconds < 0) {
            return '0:00';
        }
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    // Function to get audio duration from base64 (quick estimation)
    const getAudioDurationFromBase64 = (base64Data: string): number => {
        try {
            // Simple estimation based on base64 size for common formats
            // This is approximate since we can't easily decode without creating audio element
            const dataSize = base64Data.length * 0.75; // Convert base64 to bytes
            const estimatedDuration = Math.max(1, Math.min(60, dataSize / 16000)); // Rough estimate
            return estimatedDuration;
        } catch {
            return 0;
        }
    };

    // Function to render last message preview
    const renderLastMessagePreview = (conversation: CsConversationDTO) => {
        const lastMessage = conversation.last_message?.message || conversation.customer_last_message?.message;

        if (!lastMessage) {
            return tChat('no messages yet');
        }

        const messageType = lastMessage.message_type;

        switch (messageType) {
            case 'voice':
            case 'audio':
                const duration = lastMessage.media_content
                    ? getAudioDurationFromBase64(lastMessage.media_content)
                    : 0;
                return (
                    <div className="flex items-center space-x-1">
                        <Mic className="h-3 w-3 text-purple-500" />
                        <span>{tChat('voice message')}</span>
                        {duration > 0 && (
                            <span className="text-xs">({formatDuration(duration)})</span>
                        )}
                    </div>
                );
            case 'image':
                return (
                    <div className="flex items-center space-x-1">
                        <svg className="h-3 w-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>{tChat('image')}</span>
                    </div>
                );
            case 'video':
                return (
                    <div className="flex items-center space-x-1">
                        <svg className="h-3 w-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <span>{tChat('video')}</span>
                    </div>
                );
            case 'file':
                return (
                    <div className="flex items-center space-x-1">
                        <svg className="h-3 w-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>{tChat('file')}</span>
                    </div>
                );
            case 'location':
                return (
                    <div className="flex items-center space-x-1">
                        <svg className="h-3 w-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span>{tChat('location')}</span>
                    </div>
                );
            default:
                return lastMessage.text || tChat('message');
        }
    };

    // Handle pagination
    const handlePageChange = (newPage: number) => {
        currentPage.set(newPage);
        sendRequest({
            page: newPage,
            size: pageSize,
            filter: getBackendFilter(activeFilter.value),
        });
    };

    // Handle search with debouncing would be ideal, but for now we'll filter client-side
    const handleSearch = (query: string) => {
        searchQuery.set(query);
    };

    // Handle pin/unpin conversation
    const handleTogglePin = async (conversation: CsConversationDTO, e: React.MouseEvent) => {
        e.stopPropagation();
        try {
            const resp = await updateCsConversation(conversation.id, {
                pinned: !conversation.pinned
            });
            if (resp.data?.success) {
                alert.successSave()
                refresh();
            }
        } catch (error: any) {
            handleResponse(error.response)
        }
    };

    // Handle delete conversation
    const handleDeleteConversation = async (conversation: CsConversationDTO, e: React.MouseEvent) => {
        e.stopPropagation();
        deleteModal.openDeleteModal(async () => {
            try {
                const resp = await deleteCsConversation(conversation.id);
                if (resp.data?.success) {
                    alert.successDelete()
                    refresh();
                }
            } catch (error: any) {
                handleResponse(error.response)
            }
        })
    };

    // Handle assign conversation
    const handleAssignConversation = async (conversation: CsConversationDTO, e: React.MouseEvent) => {
        e.stopPropagation();
        onAssignConversation(conversation);
    };

    return (
        <div className="w-80 max-w-[85vw] md:max-w-none bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full rounded-l-lg rounded-tr-lg md:rounded-l-none md:rounded-tr-none">
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{tChat('chats')}</h2>
                        {!isConnected && (
                            <div className="w-2 h-2 bg-red-500 rounded-full" title={tChat('disconnected')} />
                        )}
                        {(isLoading || isRefreshing) && (
                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                        )}
                    </div>
                    <div className="flex items-center space-x-1">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={refresh}
                            className="h-8 w-8"
                            disabled={isLoading || isRefreshing}
                        >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => isCreateDialogOpen.set(true)}
                            className="h-8 w-8"
                        >
                            <Plus className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Search */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                        placeholder={tChat("search conversations")}
                        value={searchQuery.value}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="pl-10 h-9"
                    />
                </div>

                {/* Filter Tags */}
                <div className="mt-3 flex space-x-2 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                    {FILTER_OPTIONS.map((option) => (
                        <button
                            key={option.key}
                            onClick={() => handleFilterChange(option.key)}
                            className={`
                                inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium whitespace-nowrap transition-all duration-200 ease-in-out
                                ${activeFilter.value === option.key
                                    ? 'bg-purple-100 text-purple-700 border border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800 shadow-sm'
                                    : 'bg-gray-100 text-gray-600 border border-transparent hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
                                }
                            `}
                            title={tChat(option.descriptionKey)}
                        >
                            <span>{tChat(option.labelKey)}</span>
                            {/* Show count badge for active filters */}
                            {activeFilter.value === option.key && (
                                <span className="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs font-bold bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400 rounded-full">
                                    {filteredConversations.length}
                                </span>
                            )}
                        </button>
                    ))}
                </div>

                {/* Error message */}
                {error && (
                    <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {error}
                    </div>
                )}
            </div>

            {/* Conversations List */}
            <div className="flex-1 overflow-y-auto">
                {isLoading && conversations.length === 0 ? (
                    <div className="flex items-center justify-center h-32">
                        <div className="text-gray-500 dark:text-gray-400">{tChat('loading conversations')}</div>
                    </div>
                ) : filteredConversations.length === 0 ? (
                    <div className="flex items-center justify-center h-32">
                        <div className="text-gray-500 dark:text-gray-400">
                            {searchQuery.value ? tChat('no conversations found') : tChat('no conversations yet')}
                        </div>
                    </div>
                ) : (
                    filteredConversations.map((conversation) => (
                        <div
                            key={conversation.id}
                            onClick={() => {
                                // Send read message to mark conversation as read
                                sendMessage({
                                    type: "read",
                                    conversation_id: conversation.id
                                });

                                // Call the original conversation select handler
                                onConversationSelect(conversation.id.toString(), conversation);
                            }}
                            className={`group p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-800 transition-colors ${selectedConversationId === conversation.id.toString() ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800' : ''
                                }`}
                        >
                            <div className="flex items-center space-x-3">
                                {/* Avatar */}
                                <div className="relative">
                                    <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {conversation.customer?.avatar ? (
                                            <img
                                                src={conversation.customer.avatar}
                                                alt={getCustomerName(conversation)}
                                                className="w-full h-full rounded-full object-cover"
                                            />
                                        ) : (
                                            getInitials(getCustomerName(conversation))
                                        )}
                                    </div>
                                    {/* Show pinned indicator instead of online status */}
                                    {conversation.pinned && (
                                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 border-2 border-white dark:border-gray-900 rounded-full flex items-center justify-center">
                                            <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    )}
                                </div>

                                {/* Content */}
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                                            <h3 className={`text-sm font-medium truncate ${conversation.unread_count > 0 ? 'text-gray-900 dark:text-white font-semibold' : 'text-gray-900 dark:text-white'}`}>
                                                {getCustomerName(conversation)}
                                            </h3>
                                            {/* Assignment indicator */}
                                            {conversation.customer_service && (
                                                <div title={`${tChat('assigned to')} ${conversation.customer_service.user?.display_name || conversation.customer_service.user?.email || tChat('unknown user')}`}>
                                                    <UserCheck className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                                </div>
                                            )}
                                            {/* Unread count badge */}
                                            {conversation.unread_count > 0 && (
                                                <div className="inline-flex items-center justify-center min-w-[18px] h-[18px] px-1 text-xs font-bold text-white bg-blue-500 rounded-full flex-shrink-0">
                                                    {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                                                </div>
                                            )}
                                            {/* Unread indicator */}
                                            {hasUnreadMessages(conversation) && (
                                                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                            )}
                                        </div >
                                        <div className="flex items-center space-x-2">
                                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                                {formatTimestamp(conversation.last_message?.message?.timestamp || conversation.conversation?.last_message?.timestamp)}
                                            </span>
                                            {/* Dropdown Menu */}
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                                        onClick={(e) => e.stopPropagation()}
                                                    >
                                                        <MoreVertical className="h-3 w-3" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end" className="w-40">
                                                    <DropdownMenuItem
                                                        onClick={(e) => handleTogglePin(conversation, e)}
                                                        className="flex items-center space-x-2"
                                                    >
                                                        {conversation.pinned ? (
                                                            <>
                                                                <PinOff className="h-4 w-4" />
                                                                <span>{tChat('unpin')}</span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <Pin className="h-4 w-4" />
                                                                <span>{tChat('pin')}</span>
                                                            </>
                                                        )}
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={(e) => handleAssignConversation(conversation, e)}
                                                        className="flex items-center space-x-2"
                                                    >
                                                        <UserPlus className="h-4 w-4" />
                                                        <span>{tChat('assign to')}</span>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={(e) => handleDeleteConversation(conversation, e)}
                                                        className="flex items-center space-x-2 text-red-600 focus:text-red-600"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                        <span>{tChat('delete')}</span>
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </div >
                                    <div className="flex items-center justify-between mt-1">
                                        <div className={`text-sm truncate ${conversation.unread_count > 0 ? 'text-gray-900 dark:text-gray-200 font-medium' : 'text-gray-600 dark:text-gray-300'}`}>
                                            {renderLastMessagePreview(conversation)}
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {/* WhatsApp source indicator */}
                                            <div className="flex items-center">
                                                <FaIcon
                                                    icon="whatsapp"
                                                    className="fa-brands fa-whatsapp text-green-500 text-xs"
                                                />
                                            </div>
                                            {/* Show conversation status */}
                                            {conversation.closed_at > 0 && (
                                                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700 rounded-full">
                                                    Closed
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div >
                            </div >
                        </div >
                    ))
                )}
            </div >

            {/* Pagination */}
            {
                wsPage && wsPage.total > pageSize && (
                    <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                        <Pagination page={{
                            value: wsPage,
                            set: (newPage: any) => {
                                if (typeof newPage === 'function') {
                                    const updated = newPage(wsPage);
                                    handlePageChange(updated.page);
                                } else {
                                    handlePageChange(newPage.page);
                                }
                            }
                        }} />
                    </div>
                )
            }
            {/* Create Conversation Dialog */}
            <CreateConversationDialog
                open={isCreateDialogOpen.value}
                onOpenChange={isCreateDialogOpen.set}
                onSuccess={() => {
                    refresh()
                }}
            />


        </div >
    );
};

export default ChatSidebar;