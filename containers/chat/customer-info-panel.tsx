import { CustomerDTO } from "@/apis/cs_chat_v1/models";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
    User,
    Mail,
    Phone,
    MapPin,
    Building,
    FileText,
    X,
    Calendar,
    Tag,
    Copy,
    Check
} from "lucide-react";
import { useState } from "@/lib/react";
import FaIcon from "@/containers/layout/fa-icon";
import useTranslation from "next-translate/useTranslation";

interface CustomerInfoPanelProps {
    customer: CustomerDTO;
    isOpen: boolean;
    onClose: () => void;
}

const CustomerInfoPanel = ({ customer, isOpen, onClose }: CustomerInfoPanelProps) => {
    const { t: tChat } = useTranslation('chat');
    const copiedField = useState<string | null>(null);

    const formatDate = (timestamp: number) => {
        if (!timestamp) return 'N/A';
        return new Date(timestamp * 1000).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getInitials = (customer: CustomerDTO) => {
        const name = customer.display_name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim();
        return name.split(' ').map(n => n[0]).join('').toUpperCase() || 'UC';
    };

    const getFullName = (customer: CustomerDTO) => {
        if (customer.display_name) return customer.display_name;
        return `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || tChat('unknown customer');
    };

    const copyToClipboard = async (text: string, field: string) => {
        try {
            await navigator.clipboard.writeText(text);
            copiedField.set(field);
            setTimeout(() => copiedField.set(null), 2000);
        } catch (err) {
            console.error('Failed to copy:', err);
        }
    };

    const InfoRow = ({
        icon: Icon,
        label,
        value,
        copyable = false,
        fieldKey = ""
    }: {
        icon: any,
        label: string,
        value: string | undefined,
        copyable?: boolean,
        fieldKey?: string
    }) => {
        if (!value || value.trim() === '') return null;

        return (
            <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02]">
                <Icon className="h-4 w-4 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                        {label}
                    </p>
                    <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-900 dark:text-white break-words">
                            {value}
                        </p>
                        {copyable && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 ml-2 flex-shrink-0 transition-all duration-200 hover:scale-110"
                                onClick={() => copyToClipboard(value, fieldKey)}
                            >
                                {copiedField.value === fieldKey ? (
                                    <Check className="h-3 w-3 text-green-500 animate-pulse" />
                                ) : (
                                    <Copy className="h-3 w-3" />
                                )}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    if (!isOpen) {
        return null;
    }

    return (
        <div className={`
            fixed md:relative inset-0 md:inset-auto z-50 md:z-auto w-full md:w-80
            bg-white dark:bg-gray-900 
            md:border-l border-gray-200 dark:border-gray-700 
            flex flex-col h-full 
            transition-all duration-300 ease-in-out transform 
            translate-x-0 opacity-100
        `}
            style={{
                overflow: 'hidden'
            }}
        >
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {tChat('customer info')}
                    </h3>
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={onClose}
                        className="h-8 w-8 transition-all duration-200 hover:scale-110 hover:rotate-90"
                    >
                        <X className="h-4 w-4" />
                    </Button>
                </div>
            </div>

            <ScrollArea className="flex-1">
                <div className="p-4 space-y-6 animate-fadeIn">
                    {/* Profile Section */}
                    <Card className="p-4 transform transition-all duration-300 ease-out hover:shadow-md">
                        <div className="flex items-center space-x-4">
                            <div className="relative">
                                <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-lg font-medium text-gray-700 dark:text-gray-300">
                                    {customer.avatar ? (
                                        <img
                                            src={customer.avatar}
                                            alt={getFullName(customer)}
                                            className="w-full h-full rounded-full object-cover"
                                        />
                                    ) : (
                                        getInitials(customer)
                                    )}
                                </div>
                                {customer.active && (
                                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white dark:border-gray-900 rounded-full" />
                                )}
                            </div>
                            <div className="flex-1 min-w-0">
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                    {getFullName(customer)}
                                </h4>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {tChat('customer id')}: {customer.id}
                                </p>
                                <div className="flex items-center mt-1">
                                    <FaIcon
                                        icon="whatsapp"
                                        className="fa-brands fa-whatsapp text-green-500 text-sm mr-2"
                                    />
                                    <span className="text-xs text-gray-500 dark:text-gray-400">{tChat('whatsapp customer')}</span>
                                </div>
                            </div>
                        </div>

                        {/* Tags */}
                        {customer.tags && customer.tags.length > 0 && (
                            <div className="mt-4">
                                <div className="flex items-center space-x-2 mb-2">
                                    <Tag className="h-4 w-4 text-gray-500" />
                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                        {tChat('tags')}
                                    </span>
                                </div>
                                <div className="flex flex-wrap gap-2">
                                    {customer.tags.map((tag, index) => (
                                        <Badge
                                            key={tag.id}
                                            variant="secondary"
                                            className="text-xs transition-all duration-200 hover:scale-105 animate-fadeIn"
                                            style={{
                                                backgroundColor: tag.color + '20',
                                                color: tag.color,
                                                animationDelay: `${index * 100}ms`
                                            }}
                                        >
                                            {tag.name}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        )}
                    </Card>

                    {/* Contact Information */}
                    <div className="space-y-1">
                        <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                            {tChat('contact information')}
                        </h5>

                        <InfoRow
                            icon={Mail}
                            label={tChat("email")}
                            value={customer.email}
                            copyable={true}
                            fieldKey="email"
                        />

                        <InfoRow
                            icon={Phone}
                            label={tChat("phone")}
                            value={customer.phone_code && customer.phone ? `${customer.phone_code} ${customer.phone}` : customer.phone}
                            copyable={true}
                            fieldKey="phone"
                        />
                    </div>

                    {/* Company Information */}
                    {customer.company_name && (
                        <div className="space-y-1">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                                {tChat('company information')}
                            </h5>

                            <InfoRow
                                icon={Building}
                                label={tChat("company")}
                                value={customer.company_name}
                                copyable={true}
                                fieldKey="company"
                            />
                        </div>
                    )}

                    {/* Address Information */}
                    {(customer.address_line_1 || customer.city || customer.state || customer.country) && (
                        <div className="space-y-1">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                                {tChat('address')}
                            </h5>

                            <InfoRow
                                icon={MapPin}
                                label={tChat("address")}
                                value={[
                                    customer.address_line_1,
                                    customer.address_line_2,
                                    customer.city,
                                    customer.state,
                                    customer.postal_code,
                                    customer.country
                                ].filter(Boolean).join(', ')}
                                copyable={true}
                                fieldKey="address"
                            />
                        </div>
                    )}

                    {/* Notes */}
                    {customer.note && (
                        <div className="space-y-1">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                                {tChat('notes')}
                            </h5>

                            <InfoRow
                                icon={FileText}
                                label={tChat("customer notes")}
                                value={customer.note}
                                copyable={true}
                                fieldKey="notes"
                            />
                        </div>
                    )}

                    {/* Metadata */}
                    <div className="space-y-1">
                        <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                            {tChat('account details')}
                        </h5>

                        <InfoRow
                            icon={Calendar}
                            label={tChat("created")}
                            value={formatDate(customer.created_at)}
                        />

                        <InfoRow
                            icon={Calendar}
                            label={tChat("last updated")}
                            value={formatDate(customer.updated_at)}
                        />

                        <div className="flex items-start space-x-3 p-3">
                            <User className="h-4 w-4 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
                            <div className="flex-1">
                                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                    {tChat('status')}
                                </p>
                                <div className="mt-1">
                                    <Badge variant={customer.active ? "default" : "secondary"}>
                                        {customer.active ? tChat("active") : tChat("inactive")}
                                    </Badge>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ScrollArea>
        </div>
    );
};

export default CustomerInfoPanel;