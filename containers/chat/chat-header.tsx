import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ArrowLeft, Info, Menu, MoreVertical, Phone, User, Video, UserPlus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { CsConversationDTO } from "@/apis/cs_chat_v1/models";
import useTranslation from "next-translate/useTranslation";

interface Conversation {
    id: string;
    name: string;
    avatar?: string;
    isOnline: boolean;
    lastSeen?: string;
    participantCount?: number;
    isGroup?: boolean;
}

interface IProps {
    conversation?: Conversation;
    csConversation?: CsConversationDTO;
    onVoiceCall?: () => void;
    onVideoCall?: () => void;
    onMoreOptions?: () => void;
    onBackClick?: () => void;
    onMenuClick?: () => void;
    onCustomerInfoToggle?: () => void;
    isCustomerInfoOpen?: boolean;
    onAssignConversation?: () => void;
    onDeleteConversation?: () => void;
}

const ChatHeader = ({ conversation, csConversation, onVoiceCall, onVideoCall, onMoreOptions, onBackClick, onMenuClick, onCustomerInfoToggle, isCustomerInfoOpen, onAssignConversation, onDeleteConversation }: IProps) => {
    const { t: tChat } = useTranslation('chat');
    const [timeRemaining, setTimeRemaining] = useState<string>("");

    // Calculate session status and countdown
    useEffect(() => {
        if (!csConversation) return;

        const updateTimer = () => {
            // If conversation is closed
            if (csConversation.closed_at > 0) {
                setTimeRemaining(tChat("session closed"));
                return;
            }

            // Get customer's last message timestamp
            const customerLastMessageTimestamp = csConversation.customer_last_message?.message?.timestamp;

            if (!customerLastMessageTimestamp) {
                setTimeRemaining(tChat("no customer messages"));
                return;
            }

            // Calculate time remaining (24 hours from last customer message)
            const now = Math.floor(Date.now() / 1000); // Current time in seconds
            const lastMessageTime = customerLastMessageTimestamp; // Already in seconds
            const elapsed = now - lastMessageTime;
            const totalSessionDuration = 24 * 60 * 60; // 24 hours in seconds
            const remaining = totalSessionDuration - elapsed;

            if (remaining <= 0) {
                setTimeRemaining(tChat("session expired"));
                return;
            }

            // Format remaining time
            const hours = Math.floor(remaining / 3600);
            const minutes = Math.floor((remaining % 3600) / 60);
            const seconds = remaining % 60;

            if (hours > 0) {
                setTimeRemaining(`${hours}h ${minutes}m ${tChat("remaining")}`);
            } else if (minutes > 0) {
                setTimeRemaining(`${minutes}m ${seconds}s ${tChat("remaining")}`);
            } else {
                setTimeRemaining(`${seconds}s ${tChat("remaining")}`);
            }
        };

        // Update immediately
        updateTimer();

        // Set up interval to update every second for active sessions
        if (csConversation.closed_at === 0) {
            const interval = setInterval(updateTimer, 1000);
            return () => clearInterval(interval);
        }
    }, [csConversation]);

    if (!conversation) {
        return (
            <div className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
                {/* Mobile menu button - shown when no conversation selected */}
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={onMenuClick}
                    className="h-8 w-8 md:hidden text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                    <Menu className="h-4 w-4" />
                </Button>

                <p className="text-gray-500 dark:text-gray-400 flex-1 text-center md:text-left">
                    {tChat("select a conversation to start chatting")}
                </p>

                {/* Spacer for mobile */}
                <div className="w-8 md:hidden"></div>
            </div>
        );
    }

    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    };

    const getStatusColor = () => {
        if (!csConversation) return "text-gray-500 dark:text-gray-400";

        if (csConversation.closed_at > 0) {
            return "text-red-500 dark:text-red-400";
        }

        if (timeRemaining.includes("Expired")) {
            return "text-red-500 dark:text-red-400";
        }

        if (timeRemaining.includes("remaining")) {
            // Parse hours to determine color
            const hoursMatch = timeRemaining.match(/(\d+)h/);
            const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;

            if (hours < 1) {
                return "text-orange-500 dark:text-orange-400"; // Less than 1 hour - orange
            } else if (hours < 6) {
                return "text-yellow-500 dark:text-yellow-400"; // Less than 6 hours - yellow
            } else {
                return "text-green-500 dark:text-green-400"; // More than 6 hours - green
            }
        }

        return "text-gray-500 dark:text-gray-400";
    };

    return (
        <div className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
            {/* Left side - Conversation info */}
            <div className="flex items-center space-x-3">
                {/* Mobile back button - shown when conversation is selected */}
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={onBackClick}
                    className="h-8 w-8 md:hidden text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                {/* Avatar */}
                <div className="relative">
                    <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium text-gray-700 dark:text-gray-300">
                        {conversation.avatar ? (
                            <img
                                src={conversation.avatar}
                                alt={conversation.name}
                                className="w-full h-full rounded-full object-cover"
                            />
                        ) : conversation.isGroup ? (
                            <User className="h-5 w-5" />
                        ) : (
                            getInitials(conversation.name)
                        )}
                    </div>
                    {/* Session status indicator dot */}
                    {csConversation && (
                        <div className={`absolute bottom-0 right-0 w-3 h-3 border-2 border-white dark:border-gray-900 rounded-full ${csConversation.closed_at > 0
                            ? 'bg-red-500'
                            : timeRemaining.includes("Expired")
                                ? 'bg-red-500'
                                : timeRemaining.includes("remaining")
                                    ? 'bg-green-500'
                                    : 'bg-gray-400'
                            }`}></div>
                    )}
                </div>

                {/* Name and session status */}
                <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        {conversation.name}
                    </h3>
                    <div className="flex items-center space-x-2">
                        <p className={`text-xs font-medium ${getStatusColor()}`}>
                            {timeRemaining || tChat("loading")}
                        </p>
                        {/* Assignment info */}
                        {csConversation?.customer_service && (
                            <div className="flex items-center space-x-1">
                                <span className="text-xs text-gray-400">•</span>
                                <span className="text-xs text-gray-600 dark:text-gray-400">
                                    {tChat("assigned to")} {csConversation.customer_service.user?.display_name || csConversation.customer_service.user?.email || tChat('unknown user')}
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Right side - Action buttons */}
            <div className="flex items-center space-x-2">
                {/* Customer Info Toggle */}
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={onCustomerInfoToggle}
                    className={`h-8 w-8 ${isCustomerInfoOpen
                        ? 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                    title={tChat("customer information")}
                >
                    <Info className="h-4 w-4" />
                </Button>

                {/* {!conversation.isGroup && (
                    <>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={onVoiceCall}
                            className="h-8 w-8 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                        >
                            <Phone className="h-4 w-4" />
                        </Button>

                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={onVideoCall}
                            className="h-8 w-8 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                        >
                            <Video className="h-4 w-4" />
                        </Button>
                    </>
                )} */}

                {/* More Options Dropdown */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                        >
                            <MoreVertical className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem
                            onClick={onAssignConversation}
                            className="flex items-center space-x-2"
                        >
                            <UserPlus className="h-4 w-4" />
                            <span>{tChat("assign to")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={onDeleteConversation}
                            className="flex items-center space-x-2 text-red-600 focus:text-red-600"
                        >
                            <Trash2 className="h-4 w-4" />
                            <span>{tChat("delete")}</span>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
    );
};

export default ChatHeader;