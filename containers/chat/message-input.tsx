import { useState } from "@/lib/react";
import { useRef } from "react";
import { Send, Paperclip, Smile, X, File, Image, Video, Music, Camera, FileText, FolderOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import EmojiPicker, { EmojiClickData, Theme } from "emoji-picker-react";
import useTranslation from "next-translate/useTranslation";

interface FileWithPreview {
    file: File;
    id: string;
    preview?: string;
    type: 'image' | 'video' | 'audio' | 'document';
}

interface IProps {
    onSendMessage: (message: string, files?: FileWithPreview[]) => void;
    onAttachFile?: () => void;
    onToggleEmoji?: () => void;
    onVoiceMessage?: () => void;
    onStopRecording?: () => void;
    onCancelRecording?: () => void;
    disabled?: boolean;
    placeholder?: string;
    isRecording?: boolean;
}

const MessageInput = ({
    onSendMessage,
    onAttachFile,
    onToggleEmoji,
    onVoiceMessage,
    onStopRecording,
    onCancelRecording,
    disabled = false,
    placeholder,
    isRecording = false
}: IProps) => {
    const { t: tChat } = useTranslation('chat');
    const message = useState<string>("");
    const showEmojiPicker = useState<boolean>(false);
    const selectedFiles = useState<FileWithPreview[]>([]);
    const showFileMenu = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const documentInputRef = useRef<HTMLInputElement>(null);
    const mediaInputRef = useRef<HTMLInputElement>(null);

    const defaultPlaceholder = placeholder || tChat("type a message");

    const getFileType = (file: File): FileWithPreview['type'] => {
        if (file.type.startsWith('image/')) return 'image';
        if (file.type.startsWith('video/')) return 'video';
        if (file.type.startsWith('audio/')) return 'audio';
        return 'document';
    };

    const getFileIcon = (type: FileWithPreview['type']) => {
        switch (type) {
            case 'image': return Image;
            case 'video': return Video;
            case 'audio': return Music;
            default: return File;
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);

        files.forEach(file => {
            const fileType = getFileType(file);
            const fileId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

            const fileWithPreview: FileWithPreview = {
                file,
                id: fileId,
                type: fileType
            };

            // Create preview for images
            if (fileType === 'image') {
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileWithPreview.preview = e.target?.result as string;
                    selectedFiles.set(prev => [...prev, fileWithPreview]);
                };
                reader.readAsDataURL(file);
            } else {
                selectedFiles.set(prev => [...prev, fileWithPreview]);
            }
        });

        // Clear all inputs
        if (fileInputRef.current) fileInputRef.current.value = '';
        if (documentInputRef.current) documentInputRef.current.value = '';
        if (mediaInputRef.current) mediaInputRef.current.value = '';

        // Close the file menu
        showFileMenu.set(false);
    };

    const handleRemoveFile = (fileId: string) => {
        selectedFiles.set(prev => prev.filter(f => f.id !== fileId));
    };

    const handleAttachFile = () => {
        showFileMenu.set(!showFileMenu.value);
        if (onAttachFile) {
            onAttachFile();
        }
    };

    const handleFileTypeSelect = (type: 'media' | 'documents' | 'all') => {
        switch (type) {
            case 'media':
                if (mediaInputRef.current) mediaInputRef.current.click();
                break;
            case 'documents':
                if (documentInputRef.current) documentInputRef.current.click();
                break;
            case 'all':
                if (fileInputRef.current) fileInputRef.current.click();
                break;
        }
    };

    const handleSend = () => {
        if ((message.value.trim() || selectedFiles.value.length > 0) && !disabled) {
            onSendMessage(message.value.trim(), selectedFiles.value.length > 0 ? selectedFiles.value : undefined);
            message.set("");
            selectedFiles.set([]);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    const handleVoiceToggle = () => {
        if (onVoiceMessage) {
            onVoiceMessage();
        }
    };

    const handleEmojiToggle = () => {
        showEmojiPicker.set(!showEmojiPicker.value);
        if (onToggleEmoji) {
            onToggleEmoji();
        }
    };

    const handleEmojiClick = (emojiData: EmojiClickData) => {
        message.set(message.value + emojiData.emoji);
        showEmojiPicker.set(false);
    };

    return (
        <div className="px-4 md:px-6 py-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            {/* File Previews */}
            {selectedFiles.value.length > 0 && (
                <div className="mb-4 p-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {selectedFiles.value.length} {selectedFiles.value.length > 1 ? tChat('files selected') : tChat('file selected')}
                        </span>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => selectedFiles.set([])}
                            className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                            {tChat('clear all')}
                        </Button>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {selectedFiles.value.map((fileItem) => {
                            const IconComponent = getFileIcon(fileItem.type);
                            return (
                                <div key={fileItem.id} className="relative group border border-gray-200 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-gray-700/50">
                                    <div className="flex items-start space-x-3">
                                        {fileItem.type === 'image' && fileItem.preview ? (
                                            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-600 shrink-0">
                                                <img
                                                    src={fileItem.preview}
                                                    alt={fileItem.file.name}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        ) : (
                                            <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center shrink-0">
                                                <IconComponent className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                            </div>
                                        )}
                                        <div className="min-w-0 flex-1">
                                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                                {fileItem.file.name}
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                {formatFileSize(fileItem.file.size)}
                                            </p>
                                        </div>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => handleRemoveFile(fileItem.id)}
                                            className="h-6 w-6 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            <div className="flex items-end space-x-2 md:space-x-3">
                {/* Hidden File Inputs */}
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
                    onChange={handleFileSelect}
                    className="hidden"
                />
                <input
                    ref={mediaInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={handleFileSelect}
                    className="hidden"
                />
                <input
                    ref={documentInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.txt,.zip,.rar"
                    onChange={handleFileSelect}
                    className="hidden"
                />

                {/* Attach File Button with Dropdown */}
                <div className="relative">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleAttachFile}
                        disabled={disabled || isRecording}
                        className={`h-10 w-10 shrink-0 rounded-lg transition-all ${showFileMenu.value
                            ? 'text-blue-600 bg-blue-50 dark:bg-blue-500/10 dark:text-blue-400'
                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800'
                            }`}
                    >
                        <Paperclip className="h-3 w-3 md:h-4 md:w-4" />
                    </Button>

                    {/* File Type Menu */}
                    {showFileMenu.value && (
                        <div className="absolute bottom-full left-0 mb-2 z-50">
                            <div className="relative">
                                <div
                                    className="fixed inset-0 z-40"
                                    onClick={() => showFileMenu.set(false)}
                                />
                                <div className="relative z-50 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg min-w-[200px] overflow-hidden">
                                    <div className="py-2">
                                        <button
                                            onClick={() => handleFileTypeSelect('media')}
                                            className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                        >
                                            <div className="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                                                <Camera className="w-4 h-4 text-green-600 dark:text-green-400" />
                                            </div>
                                            <div className="flex-1 text-left">
                                                <div className="font-medium">{tChat('photos and videos')}</div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">{tChat('share images and videos')}</div>
                                            </div>
                                        </button>

                                        <button
                                            onClick={() => handleFileTypeSelect('documents')}
                                            className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                        >
                                            <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                                                <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                            </div>
                                            <div className="flex-1 text-left">
                                                <div className="font-medium">{tChat('documents')}</div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">{tChat('pdf word text files')}</div>
                                            </div>
                                        </button>

                                        <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>

                                        <button
                                            onClick={() => handleFileTypeSelect('all')}
                                            className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                                        >
                                            <div className="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                                                <FolderOpen className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                            </div>
                                            <div className="flex-1 text-left">
                                                <div className="font-medium">{tChat('all files')}</div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">{tChat('browse all file types')}</div>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Message Input */}
                <div className="flex-1 relative">
                    <div className="relative bg-white dark:bg-gray-800 rounded-xl border border-gray-300 dark:border-gray-600 shadow-sm hover:border-gray-400 dark:hover:border-gray-500 transition-colors focus-within:border-blue-500 dark:focus-within:border-blue-400 focus-within:ring-1 focus-within:ring-blue-500/20">
                        <Input
                            value={message.value}
                            onChange={(e) => message.set(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder={disabled ? tChat("loading") : defaultPlaceholder}
                            disabled={disabled || isRecording}
                            className="pr-10 md:pr-12 py-3 text-sm bg-transparent border-0 rounded-xl focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                            style={{
                                minHeight: '44px',
                                maxHeight: '120px',
                                overflowY: message.value.length > 100 ? 'auto' : 'hidden'
                            }}
                        />

                        {/* Emoji Button */}
                        <div className="absolute right-2 md:right-3 top-1/2 transform -translate-y-1/2">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={handleEmojiToggle}
                                disabled={disabled || isRecording}
                                className={`h-7 w-7 md:h-8 md:w-8 rounded-lg transition-all ${showEmojiPicker.value
                                    ? 'text-blue-600 bg-blue-50 dark:bg-blue-500/10 dark:text-blue-400'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700'
                                    }`}
                            >
                                <Smile className="h-3 w-3 md:h-4 md:w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Emoji Picker */}
                    {showEmojiPicker.value && (
                        <div className="absolute bottom-full right-0 mb-2 z-50">
                            <div className="relative">
                                <div
                                    className="fixed inset-0 z-40"
                                    onClick={() => showEmojiPicker.set(false)}
                                />
                                <div className="relative z-50">
                                    <EmojiPicker
                                        onEmojiClick={handleEmojiClick}
                                        autoFocusSearch={false}
                                        theme={document.documentElement.classList.contains('dark') ? Theme.DARK : Theme.LIGHT}
                                        width={window.innerWidth < 768 ? Math.min(280, window.innerWidth - 32) : 300}
                                        height={window.innerWidth < 768 ? 320 : 400}
                                        searchDisabled={false}
                                        skinTonesDisabled={false}
                                        previewConfig={{
                                            showPreview: false
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Voice Message or Send Button */}
                <Button
                    onClick={handleSend}
                    disabled={disabled || (!message.value.trim() && selectedFiles.value.length === 0)}
                    size="icon"
                    className="h-10 w-10 shrink-0 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all disabled:opacity-50"
                >
                    <Send className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
            </div>

            {/* Character count for long messages */}
            {message.value.length > 500 && (
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-right font-medium">
                    {message.value.length}/1000 {tChat('characters')}
                </div>
            )}
        </div>
    );
};

export default MessageInput;