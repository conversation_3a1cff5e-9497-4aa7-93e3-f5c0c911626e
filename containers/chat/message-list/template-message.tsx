import { MessageDTO, WhatsappTemplateParam } from "@/apis/cs_chat_v1/models";
import useTranslation from "next-translate/useTranslation";

interface TemplateMessageProps {
    message: MessageDTO;
    isCustomerService: boolean;
}

export const TemplateMessage = ({ message, isCustomerService }: TemplateMessageProps) => {
    const { t: tChat } = useTranslation('chat');
    const renderTemplateText = (template: string, params: WhatsappTemplateParam[]) => {
        if (!template || !params) return template;

        let renderedText = template;
        params.forEach((param) => {
            const placeholder = `{{${param.key}}}`;
            renderedText = renderedText.replace(
                new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'),
                param.value || ''
            );
        });

        return renderedText;
    };

    return (
        <div className="template-message">
            {/* Template indicator */}
            <div className={`text-xs mb-2 opacity-75 flex items-center gap-1 ${isCustomerService ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400'
                }`}>
                <i className="fa-light fa-template" />
                <span>{tChat('template')}: {message.template_name}</span>
                {message.template_language && (
                    <span className="text-xs opacity-60">({message.template_language})</span>
                )}
            </div>

            {/* Template Header */}
            {message.template_header && (
                <div className="template-header font-semibold mb-2">
                    {renderTemplateText(message.template_header, message.template_header_params || [])}
                </div>
            )}

            {/* Template Body */}
            {message.template_body && (
                <div className="template-body mb-2 leading-relaxed">
                    {renderTemplateText(message.template_body, message.template_body_params || [])}
                </div>
            )}

            {/* Template Footer */}
            {message.template_footer && (
                <div className={`template-footer text-xs mt-2 opacity-75 ${isCustomerService ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                    {renderTemplateText(message.template_footer, message.template_footer_params || [])}
                </div>
            )}

            {/* Regular text content if present */}
            {message.text && (
                <div className="mt-2 pt-2 border-t border-opacity-20 border-current">
                    {message.text}
                </div>
            )}
        </div>
    );
}; 