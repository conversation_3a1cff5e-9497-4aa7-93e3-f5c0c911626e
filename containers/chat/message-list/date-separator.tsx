import useTranslation from "next-translate/useTranslation";

interface DateSeparatorProps {
    date: string;
}

export const DateSeparator = ({ date }: DateSeparatorProps) => {
    const { t: tCommon } = useTranslation('common');
    const displayDate = date === new Date().toDateString() ? tCommon('today') : date;

    return (
        <div className="flex justify-center my-4">
            <span className="bg-white dark:bg-gray-700 px-3 py-1 rounded-full text-xs text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
                {displayDate}
            </span>
        </div>
    );
}; 