import { CsMessageDTO } from "@/apis/cs_chat_v1/models";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import { useCsMessageWebSocket } from "../hooks/use-cs-message-websocket";
import { validateBase64Audio } from "./audio-utils";
import { DateSeparator } from "./date-separator";
import { MessageBubble } from "./message-bubble";
import useTranslation from "next-translate/useTranslation";

interface IProps {
    conversationId?: number;
}

export interface MessageListRef {
    scrollToBottom: () => void;
}

const MessageList = forwardRef<MessageListRef, IProps>(({ conversationId }, ref) => {
    const { t: tChat } = useTranslation('chat');
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const [playingId, setPlayingId] = useState<number | null>(null);
    const [audioProgress, setAudioProgress] = useState<{ [key: number]: { current: number; duration: number } }>({});
    const [lastMessageId, setLastMessageId] = useState<number | undefined>(undefined);
    const isScrollingToBottomRef = useRef(false);
    const previousScrollHeightRef = useRef(0);
    const previousMessagesLengthRef = useRef(0);
    const lastMessageIdBeforeLoadingRef = useRef<number | undefined>(undefined);

    const { messages, isLoading, isLoadingMore, error, refresh, loadMoreMessages, canLoadMore } = useCsMessageWebSocket(conversationId);

    const scrollToBottom = () => {
        if (scrollContainerRef.current) {
            isScrollingToBottomRef.current = true;
            scrollContainerRef.current.scrollTo({
                top: scrollContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            // Reset flag after scroll animation completes
            setTimeout(() => {
                isScrollingToBottomRef.current = false;
            }, 500);
        }
    }

    // Track the last message ID before loading more messages
    useEffect(() => {
        if (isLoadingMore && messages.length > 0) {
            const currentLastMessage = messages[messages.length - 1];
            lastMessageIdBeforeLoadingRef.current = currentLastMessage.id;
        }
    }, [isLoadingMore, messages]);

    // Preserve scroll position when loading more messages
    useEffect(() => {
        const scrollContainer = scrollContainerRef.current;
        if (!scrollContainer) return;

        // If messages were added (pagination) and not just a new message at the end
        if (messages.length > previousMessagesLengthRef.current && !isScrollingToBottomRef.current) {
            const newScrollHeight = scrollContainer.scrollHeight;
            const heightDifference = newScrollHeight - previousScrollHeightRef.current;

            // Only adjust scroll if height increased (new messages loaded)
            if (heightDifference > 0) {
                scrollContainer.scrollTop = scrollContainer.scrollTop + heightDifference;
            }
        }

        previousScrollHeightRef.current = scrollContainer.scrollHeight;
        previousMessagesLengthRef.current = messages.length;
    }, [messages]);

    // Handle scroll events for pagination
    useEffect(() => {
        const scrollContainer = scrollContainerRef.current;
        if (!scrollContainer) return;

        const handleScroll = () => {
            // Don't trigger pagination if we're programmatically scrolling to bottom
            if (isScrollingToBottomRef.current) return;

            const { scrollTop } = scrollContainer;
            const threshold = 100; // Pixels from top to trigger loading

            // Check if user scrolled to near the top
            if (scrollTop <= threshold && canLoadMore() && !isLoadingMore) {
                console.log('User scrolled to top, loading more messages...');
                // Store current scroll height before loading more
                previousScrollHeightRef.current = scrollContainer.scrollHeight;
                loadMoreMessages();
            }
        };

        scrollContainer.addEventListener('scroll', handleScroll, { passive: true });

        return () => {
            scrollContainer.removeEventListener('scroll', handleScroll);
        };
    }, [canLoadMore, isLoadingMore, loadMoreMessages]);

    // Monitor last message ID and trigger scroll when it changes (only for truly new messages)
    useEffect(() => {
        if (messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            const newLastMessageId = lastMessage.id;

            // Only scroll to bottom if:
            // 1. Last message ID changed
            // 2. We're not currently loading more messages
            // 3. If we were loading more, the new last message ID should be different from what we had before loading
            const shouldScrollToBottom = lastMessageId !== newLastMessageId &&
                (!isLoadingMore && !lastMessageIdBeforeLoadingRef.current ||
                    (lastMessageIdBeforeLoadingRef.current && newLastMessageId !== lastMessageIdBeforeLoadingRef.current));

            if (shouldScrollToBottom) {
                console.log('🚀 New message detected, auto-scrolling to bottom', {
                    oldId: lastMessageId,
                    newId: newLastMessageId,
                    isLoadingMore,
                    lastMessageIdBeforeLoading: lastMessageIdBeforeLoadingRef.current
                });
                scrollToBottom();
            }

            setLastMessageId(newLastMessageId);

            // Clear the reference after loading is complete
            if (!isLoadingMore) {
                lastMessageIdBeforeLoadingRef.current = undefined;
            }
        }
    }, [messages, lastMessageId, isLoadingMore]);

    // Expose scroll function via ref
    useImperativeHandle(ref, () => ({
        scrollToBottom: scrollToBottom,
    }), []);

    // Cleanup audio on component unmount or conversation change
    useEffect(() => {
        return () => {
            setLastMessageId(undefined);
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, [conversationId]);

    // Pre-load audio metadata to get duration
    useEffect(() => {
        messages.forEach(message => {
            if (message.message?.message_type === 'voice' && message.message.media_content && !audioProgress[message.id]?.duration) {
                const loadAudioMetadata = async () => {
                    try {
                        const validation = validateBase64Audio(message.message!.media_content!);
                        if (!validation.isValid) {
                            console.warn(`Invalid audio data for message ${message.id}:`, validation.error);
                            // Set a default duration so the button is not disabled
                            setAudioProgress(prev => ({
                                ...prev,
                                [message.id]: { current: 0, duration: 1 },
                            }));

                            // Scroll to bottom after handling validation error
                            setTimeout(() => {
                                if (scrollContainerRef.current) {
                                    const isAtBottom = scrollContainerRef.current.scrollTop + scrollContainerRef.current.clientHeight >= scrollContainerRef.current.scrollHeight - 10;
                                    if (isAtBottom) {
                                        scrollContainerRef.current.scrollTo({
                                            top: scrollContainerRef.current.scrollHeight,
                                            behavior: 'smooth'
                                        });
                                    }
                                }
                            }, 100);
                            return;
                        }

                        const correctedData = validation.paddedData || message.message!.media_content!;
                        const audio = new Audio(`data:audio/webm;base64,${correctedData}`);

                        const loadPromise = new Promise((resolve, reject) => {
                            audio.addEventListener('loadedmetadata', () => {
                                if (isFinite(audio.duration) && audio.duration > 0) {
                                    resolve(audio.duration);
                                } else {
                                    reject(new Error('Invalid duration'));
                                }
                            });
                            audio.addEventListener('error', () => {
                                reject(new Error('Failed to load audio'));
                            });
                            audio.load();
                        });

                        const duration = await loadPromise as number;
                        setAudioProgress(prev => ({
                            ...prev,
                            [message.id]: { current: 0, duration },
                        }));

                        // Scroll to bottom after audio metadata loads to account for height changes
                        setTimeout(() => {
                            if (scrollContainerRef.current) {
                                const isAtBottom = scrollContainerRef.current.scrollTop + scrollContainerRef.current.clientHeight >= scrollContainerRef.current.scrollHeight - 10;
                                if (isAtBottom) {
                                    scrollContainerRef.current.scrollTo({
                                        top: scrollContainerRef.current.scrollHeight,
                                        behavior: 'smooth'
                                    });
                                }
                            }
                        }, 100);
                    } catch (error) {
                        console.warn(`Failed to load metadata for message ${message.id}:`, error);
                        // Set a default duration so the button is not disabled
                        setAudioProgress(prev => ({
                            ...prev,
                            [message.id]: { current: 0, duration: 1 },
                        }));

                        // Scroll to bottom after handling error case
                        setTimeout(() => {
                            if (scrollContainerRef.current) {
                                const isAtBottom = scrollContainerRef.current.scrollTop + scrollContainerRef.current.clientHeight >= scrollContainerRef.current.scrollHeight - 10;
                                if (isAtBottom) {
                                    scrollContainerRef.current.scrollTo({
                                        top: scrollContainerRef.current.scrollHeight,
                                        behavior: 'smooth'
                                    });
                                }
                            }
                        }, 100);
                    }
                };

                loadAudioMetadata();
            }
        });
    }, [messages, audioProgress]);

    const groupMessagesByDate = (messages: CsMessageDTO[]) => {
        const groups: { [key: string]: CsMessageDTO[] } = {};

        messages.forEach(message => {
            const date = new Date((message.message?.timestamp || 0) * 1000).toDateString();
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(message);
        });

        return groups;
    };

    const messageGroups = groupMessagesByDate(messages);

    if (isLoading) {
        return (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                <div className="text-center">
                    <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse mx-auto mb-2"></div>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">{tChat('loading messages')}</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                <div className="text-center">
                    <p className="text-red-500 text-sm">{error}</p>
                </div>
            </div>
        );
    }

    if (messages.length === 0) {
        return (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                <div className="text-center">
                    <p className="text-gray-500 dark:text-gray-400 text-sm">{tChat('no messages yet')}</p>
                </div>
            </div>
        );
    }

    return (
        <div ref={scrollContainerRef} className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-800">
            {/* Loading more indicator at the top */}
            {isLoadingMore && (
                <div className="flex items-center justify-center py-4">
                    <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                        <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse"></div>
                        <span className="text-sm">{tChat('loading more messages')}</span>
                    </div>
                </div>
            )}

            {Object.entries(messageGroups).map(([date, dayMessages]) => (
                <div key={date}>
                    <DateSeparator date={date} />

                    {/* Messages for this date */}
                    {dayMessages.map((message, index) => {
                        const isConsecutive = index > 0 &&
                            dayMessages[index - 1].message?.platform_sender_id === message.message?.platform_sender_id;

                        const isLastInGroup = index === dayMessages.length - 1 ||
                            dayMessages[index + 1].message?.platform_sender_id !== message.message?.platform_sender_id;

                        return (
                            <MessageBubble
                                key={message.id}
                                message={message}
                                isConsecutive={isConsecutive}
                                isLastInGroup={isLastInGroup}
                                playingId={playingId}
                                setPlayingId={setPlayingId}
                                audioRef={audioRef}
                                audioProgress={audioProgress}
                                setAudioProgress={setAudioProgress}
                            />
                        );
                    })}
                </div>
            ))}
            <div ref={messagesEndRef} />
        </div>
    );
});

MessageList.displayName = 'MessageList';

export default MessageList;
