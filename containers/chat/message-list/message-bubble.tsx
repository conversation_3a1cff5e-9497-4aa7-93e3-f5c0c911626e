import { CsMessageDTO } from "@/apis/cs_chat_v1/models";
import { VoiceMessage } from "./voice-message";
import { MessageStatus } from "./message-status";
import { formatTime, getInitials, detectVideoFormat } from "./audio-utils";
import { Button } from "@/components/ui/button";
import { TemplateMessage } from "./template-message";
import useTranslation from "next-translate/useTranslation";

// File type detection utility
const getFileTypeInfo = (filename: string, tChat: (key: string) => string) => {
    if (!filename) return { icon: 'fa-light fa-file', description: tChat('file'), canPreview: false };

    const extension = filename.toLowerCase().split('.').pop() || '';

    switch (extension) {
        case 'pdf':
            return { icon: 'fa-light fa-file-pdf', description: tChat('pdf document'), canPreview: true };
        case 'doc':
        case 'docx':
            return { icon: 'fa-light fa-file-word', description: tChat('word document'), canPreview: false };
        case 'xls':
        case 'xlsx':
            return { icon: 'fa-light fa-file-excel', description: tChat('excel spreadsheet'), canPreview: false };
        case 'ppt':
        case 'pptx':
            return { icon: 'fa-light fa-file-powerpoint', description: tChat('powerpoint presentation'), canPreview: false };
        case 'txt':
            return { icon: 'fa-light fa-file-lines', description: tChat('text file'), canPreview: false };
        case 'zip':
        case 'rar':
        case '7z':
            return { icon: 'fa-light fa-file-zipper', description: tChat('archive file'), canPreview: false };
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
            return { icon: 'fa-light fa-file-image', description: tChat('image file'), canPreview: false };
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'flv':
            return { icon: 'fa-light fa-file-video', description: tChat('video file'), canPreview: false };
        case 'mp3':
        case 'wav':
        case 'flac':
        case 'aac':
            return { icon: 'fa-light fa-file-audio', description: tChat('audio file'), canPreview: false };
        case 'csv':
            return { icon: 'fa-light fa-file-csv', description: tChat('csv file'), canPreview: false };
        default:
            return { icon: 'fa-light fa-file', description: tChat('file'), canPreview: false };
    }
};

interface MessageBubbleProps {
    message: CsMessageDTO;
    isConsecutive: boolean;
    isLastInGroup: boolean;
    playingId: number | null;
    setPlayingId: (id: number | null) => void;
    audioRef: React.MutableRefObject<HTMLAudioElement | null>;
    audioProgress: { [key: number]: { current: number; duration: number } };
    setAudioProgress: React.Dispatch<React.SetStateAction<{ [key: number]: { current: number; duration: number } }>>;
}

export const MessageBubble = ({
    message,
    isConsecutive,
    isLastInGroup,
    playingId,
    setPlayingId,
    audioRef,
    audioProgress,
    setAudioProgress
}: MessageBubbleProps) => {
    const { t: tChat } = useTranslation('chat');
    const isCustomerService = !!message.customer_service;
    const senderName = isCustomerService
        ? (message.customer_service?.user?.display_name || tChat('customer service'))
        : (message.customer?.display_name || tChat('customer'));

    return (
        <div className={`flex ${isCustomerService ? 'justify-end' : 'justify-start'} mb-2`}>
            <div className={`flex ${isCustomerService ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}>
                {/* Avatar */}
                {!isCustomerService && isLastInGroup && (
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {getInitials(senderName || '')}
                    </div>
                )}
                {!isCustomerService && !isLastInGroup && (
                    <div className="w-8 h-8"></div>
                )}

                {/* Message bubble */}
                <div className={`px-4 py-2 rounded-2xl ${isCustomerService
                    ? 'bg-purple-600 text-white rounded-br-md'
                    : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600 rounded-bl-md'
                    } ${isConsecutive ? 'mt-1' : 'mt-2'}`}>
                    {/* Reply context */}
                    {message.reply_to_message && (
                        <div className={`text-xs opacity-75 mb-2 p-2 rounded border-l-2 ${isCustomerService
                            ? 'border-purple-300 bg-purple-700'
                            : 'border-gray-300 dark:border-gray-500 bg-gray-100 dark:bg-gray-600'
                            }`}>
                            <div className="font-medium">
                                {message.reply_to_message.customer_service
                                    ? (message.reply_to_message.customer_service.user?.display_name || tChat('customer service'))
                                    : (message.reply_to_message.customer?.display_name || tChat('customer'))
                                }
                            </div>
                            <div className="truncate">{message.reply_to_message.message?.text}</div>
                        </div>
                    )}

                    {/* Message content */}
                    <div className="text-sm leading-relaxed">
                        {message.message?.template_name ? (
                            <TemplateMessage
                                message={message.message}
                                isCustomerService={isCustomerService}
                            />
                        ) : (message.message?.message_type === 'voice' || message.message?.message_type === 'audio') && message.message.media_content ? (
                            <VoiceMessage
                                messageId={message.id}
                                audioBase64={message.message.media_content}
                                isCustomerService={isCustomerService}
                                playingId={playingId}
                                setPlayingId={setPlayingId}
                                audioRef={audioRef}
                                audioProgress={audioProgress}
                                setAudioProgress={setAudioProgress}
                            />
                        ) : message.message?.message_type === 'image' && message.message.media_content ? (
                            <div className="max-w-xs">
                                <img
                                    src={`data:image/jpeg;base64,${message.message.media_content.split('base64,').length > 1 ? message.message.media_content.split('base64,')[1] : message.message.media_content}`}
                                    alt={tChat("image message")}
                                    className="rounded-lg w-full h-auto max-h-64 object-cover cursor-pointer"
                                    onClick={() => window.open(`data:image/jpeg;base64,${message.message?.media_content}`, '_blank')}
                                />
                                {message.message?.text && (
                                    <div className="mt-2">{message.message.text}</div>
                                )}
                            </div>
                        ) : message.message?.message_type === 'video' && message.message.media_content ? (
                            (() => {
                                const { mimeType, correctedData } = detectVideoFormat(message.message.media_content);
                                return (
                                    <div className="max-w-xs">
                                        <video
                                            src={`data:${mimeType};base64,${correctedData}`}
                                            className="rounded-lg w-full h-auto max-h-64 object-cover"
                                            controls
                                            preload="metadata"
                                        />
                                        {message.message?.text && (
                                            <div className="mt-2">{message.message.text}</div>
                                        )}
                                    </div>
                                );
                            })()
                        ) : message.message?.message_type === 'file' && message.message.media_content ? (
                            (() => {
                                const fileInfo = getFileTypeInfo(message.message.filename || '', tChat);

                                const handleFileAction = () => {
                                    if (fileInfo.canPreview) {
                                        // For PDF files, open in new tab for preview
                                        const byteCharacters = atob(message.message!.media_content!.split('base64,').length > 1 ? message.message!.media_content!.split('base64,')[1] : message.message!.media_content!);
                                        const byteNumbers = new Array(byteCharacters.length);
                                        for (let i = 0; i < byteCharacters.length; i++) {
                                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                                        }
                                        const byteArray = new Uint8Array(byteNumbers);
                                        const blob = new Blob([byteArray], { type: 'application/pdf' });
                                        const url = URL.createObjectURL(blob);
                                        window.open(url, '_blank');
                                        // Clean up the URL after a delay to allow the browser to load it
                                        setTimeout(() => URL.revokeObjectURL(url), 1000);
                                    } else {
                                        // For other files, download them
                                        const byteCharacters = atob(message.message!.media_content!);
                                        const byteNumbers = new Array(byteCharacters.length);
                                        for (let i = 0; i < byteCharacters.length; i++) {
                                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                                        }
                                        const byteArray = new Uint8Array(byteNumbers);
                                        const blob = new Blob([byteArray]);
                                        const url = URL.createObjectURL(blob);
                                        const link = document.createElement('a');
                                        link.href = url;
                                        link.download = message.message!.filename || 'document';
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                        URL.revokeObjectURL(url);
                                    }
                                };

                                return (
                                    <div className="max-w-xs">
                                        <div className="flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-600 rounded-lg">
                                            <i className={`${fileInfo.icon} text-2xl text-gray-500 dark:text-gray-400 flex-shrink-0`} />
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium truncate text-gray-900 dark:text-white">
                                                    {message.message.filename || tChat('document')}
                                                </p>
                                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                                    {fileInfo.description}
                                                </p>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0 flex-shrink-0"
                                                onClick={handleFileAction}
                                                title={fileInfo.canPreview ? tChat('preview file') : tChat('download file')}
                                            >
                                                <i className={`fa-light ${fileInfo.canPreview ? 'fa-eye' : 'fa-download'} text-sm text-gray-700 dark:text-gray-300`} />
                                            </Button>
                                        </div>
                                        {message.message?.text && (
                                            <div className="mt-2">{message.message.text}</div>
                                        )}
                                    </div>
                                );
                            })()
                        ) : (
                            message.message?.text
                        )}
                    </div>

                    {/* Timestamp and status */}
                    <div className={`flex items-center justify-end mt-1 space-x-1 text-xs ${isCustomerService ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                        <span>{formatTime(message.message?.timestamp || 0)}</span>
                        {isCustomerService && <MessageStatus status={message.message?.status || ''} />}
                    </div>
                </div>
            </div>
        </div>
    );
}; 