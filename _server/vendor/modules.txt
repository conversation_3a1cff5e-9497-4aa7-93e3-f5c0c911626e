# filippo.io/edwards25519 v1.1.0
## explicit; go 1.20
filippo.io/edwards25519
filippo.io/edwards25519/field
# github.com/bradfitz/gomemcache v0.0.0-20250403215159-8d39553ac7cf
## explicit; go 1.18
github.com/bradfitz/gomemcache/memcache
# github.com/brianvoe/gofakeit/v7 v7.2.1
## explicit; go 1.22
github.com/brianvoe/gofakeit/v7
github.com/brianvoe/gofakeit/v7/data
github.com/brianvoe/gofakeit/v7/source
# github.com/bytedance/sonic v1.13.3
## explicit; go 1.17
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/compat
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder/api
github.com/bytedance/sonic/internal/decoder/consts
github.com/bytedance/sonic/internal/decoder/errors
github.com/bytedance/sonic/internal/decoder/jitdec
github.com/bytedance/sonic/internal/decoder/optdec
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/encoder/alg
github.com/bytedance/sonic/internal/encoder/ir
github.com/bytedance/sonic/internal/encoder/vars
github.com/bytedance/sonic/internal/encoder/vm
github.com/bytedance/sonic/internal/encoder/x86
github.com/bytedance/sonic/internal/envs
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/optcaching
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/internal/utils
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.2.4
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/iasm/expr
github.com/bytedance/sonic/loader/internal/iasm/x86_64
github.com/bytedance/sonic/loader/internal/rt
# github.com/cespare/xxhash/v2 v2.3.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cloudwego/base64x v0.1.5
## explicit; go 1.16
github.com/cloudwego/base64x
github.com/cloudwego/base64x/internal/native
github.com/cloudwego/base64x/internal/native/avx2
github.com/cloudwego/base64x/internal/native/sse
github.com/cloudwego/base64x/internal/rt
# github.com/gabriel-vasile/mimetype v1.4.9
## explicit; go 1.23.0
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/gin-contrib/cache v1.4.0
## explicit; go 1.23.0
github.com/gin-contrib/cache
github.com/gin-contrib/cache/persistence
github.com/gin-contrib/cache/utils
# github.com/gin-contrib/cors v1.7.6
## explicit; go 1.23.0
github.com/gin-contrib/cors
# github.com/gin-contrib/sse v1.1.0
## explicit; go 1.23
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.10.1
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.26.0
## explicit; go 1.20
github.com/go-playground/validator/v10
# github.com/go-sql-driver/mysql v1.9.3
## explicit; go 1.21.0
github.com/go-sql-driver/mysql
# github.com/goccy/go-json v0.10.5
## explicit; go 1.19
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt/v5 v5.2.2
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/gomodule/redigo v1.9.2
## explicit; go 1.17
github.com/gomodule/redigo/redis
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/gorilla/websocket v1.5.3
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgx/v5 v5.7.5
## explicit; go 1.23.0
github.com/jackc/pgx/v5
github.com/jackc/pgx/v5/internal/iobufpool
github.com/jackc/pgx/v5/internal/pgio
github.com/jackc/pgx/v5/internal/sanitize
github.com/jackc/pgx/v5/internal/stmtcache
github.com/jackc/pgx/v5/pgconn
github.com/jackc/pgx/v5/pgconn/ctxwatch
github.com/jackc/pgx/v5/pgconn/internal/bgreader
github.com/jackc/pgx/v5/pgproto3
github.com/jackc/pgx/v5/pgtype
github.com/jackc/pgx/v5/pgxpool
github.com/jackc/pgx/v5/stdlib
# github.com/jackc/puddle/v2 v2.2.2
## explicit; go 1.19
github.com/jackc/puddle/v2
github.com/jackc/puddle/v2/internal/genstack
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/cpuid/v2 v2.2.11
## explicit; go 1.22
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/matoous/go-nanoid v1.5.1
## explicit; go 1.15
github.com/matoous/go-nanoid
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/mattn/go-sqlite3 v1.14.28
## explicit; go 1.19
github.com/mattn/go-sqlite3
# github.com/memcachier/mc/v3 v3.0.3
## explicit; go 1.12
github.com/memcachier/mc/v3
# github.com/metadiv-tech/aes v1.2.3
## explicit; go 1.23.2
github.com/metadiv-tech/aes
# github.com/metadiv-tech/base64static v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/base64static
# github.com/metadiv-tech/env v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/env
# github.com/metadiv-tech/http_call v1.2.0
## explicit; go 1.23.2
github.com/metadiv-tech/http_call
# github.com/metadiv-tech/instagram_api v1.0.0
## explicit; go 1.23.2
github.com/metadiv-tech/instagram_api
# github.com/metadiv-tech/jwt v1.1.1
## explicit; go 1.23.2
github.com/metadiv-tech/jwt
# github.com/metadiv-tech/logger v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/logger
github.com/metadiv-tech/logger/internal/file
github.com/metadiv-tech/logger/internal/msg
# github.com/metadiv-tech/metagin v1.5.4
## explicit; go 1.23.2
github.com/metadiv-tech/metagin
github.com/metadiv-tech/metagin/base
github.com/metadiv-tech/metagin/internal/context
github.com/metadiv-tech/metagin/internal/openapi
github.com/metadiv-tech/metagin/internal/typescript
github.com/metadiv-tech/metagin/jwt_auth
# github.com/metadiv-tech/metaorm v1.2.26
## explicit; go 1.23.2
github.com/metadiv-tech/metaorm
github.com/metadiv-tech/metaorm/base
github.com/metadiv-tech/metaorm/conn
github.com/metadiv-tech/metaorm/executor
github.com/metadiv-tech/metaorm/qbuild
github.com/metadiv-tech/metaorm/repository
github.com/metadiv-tech/metaorm/types
# github.com/metadiv-tech/mime v1.1.1
## explicit; go 1.23.2
github.com/metadiv-tech/mime
# github.com/metadiv-tech/mod_chat v1.6.0
## explicit; go 1.23.2
github.com/metadiv-tech/mod_chat
github.com/metadiv-tech/mod_chat/actions
github.com/metadiv-tech/mod_chat/configs
github.com/metadiv-tech/mod_chat/endpoints/chat
github.com/metadiv-tech/mod_chat/endpoints/cs_chat
github.com/metadiv-tech/mod_chat/endpoints/instagram
github.com/metadiv-tech/mod_chat/endpoints/whatsapp_broadcast
github.com/metadiv-tech/mod_chat/endpoints/whatsapp_template
github.com/metadiv-tech/mod_chat/entities
github.com/metadiv-tech/mod_chat/errors
github.com/metadiv-tech/mod_chat/services/chat_service
github.com/metadiv-tech/mod_chat/services/cs_chat_service
github.com/metadiv-tech/mod_chat/services/instagram_service
github.com/metadiv-tech/mod_chat/services/whatsapp_service
github.com/metadiv-tech/mod_chat/services/whatsapp_template_service
# github.com/metadiv-tech/mod_data v1.1.1
## explicit; go 1.23.2
github.com/metadiv-tech/mod_data
github.com/metadiv-tech/mod_data/configs
github.com/metadiv-tech/mod_data/endpoints/country
github.com/metadiv-tech/mod_data/endpoints/phone_code
github.com/metadiv-tech/mod_data/endpoints/timezone
github.com/metadiv-tech/mod_data/entities
github.com/metadiv-tech/mod_data/errors
github.com/metadiv-tech/mod_data/services/country_service
github.com/metadiv-tech/mod_data/services/phone_code_service
github.com/metadiv-tech/mod_data/services/timezone_service
# github.com/metadiv-tech/mod_relationship v1.1.2
## explicit; go 1.23.2
github.com/metadiv-tech/mod_relationship
github.com/metadiv-tech/mod_relationship/actions
github.com/metadiv-tech/mod_relationship/endpoints/customer
github.com/metadiv-tech/mod_relationship/entities
github.com/metadiv-tech/mod_relationship/errors
github.com/metadiv-tech/mod_relationship/services/customer_service
# github.com/metadiv-tech/mod_saas v1.4.19
## explicit; go 1.23.2
github.com/metadiv-tech/mod_saas
github.com/metadiv-tech/mod_saas/configs
github.com/metadiv-tech/mod_saas/endpoints/admin_management
github.com/metadiv-tech/mod_saas/endpoints/admin_self
github.com/metadiv-tech/mod_saas/endpoints/agent_management
github.com/metadiv-tech/mod_saas/endpoints/auth_admin
github.com/metadiv-tech/mod_saas/endpoints/auth_user
github.com/metadiv-tech/mod_saas/endpoints/notification
github.com/metadiv-tech/mod_saas/endpoints/system
github.com/metadiv-tech/mod_saas/endpoints/user_management
github.com/metadiv-tech/mod_saas/endpoints/user_self
github.com/metadiv-tech/mod_saas/endpoints/workspace_management
github.com/metadiv-tech/mod_saas/endpoints/workspace_management_by_admin
github.com/metadiv-tech/mod_saas/entities
github.com/metadiv-tech/mod_saas/environments
github.com/metadiv-tech/mod_saas/errors
github.com/metadiv-tech/mod_saas/requests
github.com/metadiv-tech/mod_saas/services/admin_auth_service
github.com/metadiv-tech/mod_saas/services/admin_service
github.com/metadiv-tech/mod_saas/services/notification_service
github.com/metadiv-tech/mod_saas/services/user_auth_email_service
github.com/metadiv-tech/mod_saas/services/user_auth_service
github.com/metadiv-tech/mod_saas/services/user_service
github.com/metadiv-tech/mod_saas/services/workspace_auth_service
github.com/metadiv-tech/mod_saas/services/workspace_service
github.com/metadiv-tech/mod_saas/services/workspace_usage_service
github.com/metadiv-tech/mod_saas/system_settings
# github.com/metadiv-tech/mod_salesforce v0.0.0-**************-bec99cc608dd
## explicit; go 1.23.2
github.com/metadiv-tech/mod_salesforce
github.com/metadiv-tech/mod_salesforce/actions
github.com/metadiv-tech/mod_salesforce/configs
github.com/metadiv-tech/mod_salesforce/endpoints/salesforce_account
github.com/metadiv-tech/mod_salesforce/entities
github.com/metadiv-tech/mod_salesforce/errors
github.com/metadiv-tech/mod_salesforce/services/salesforce_account_service
github.com/metadiv-tech/mod_salesforce/services/salesforce_auth_service
github.com/metadiv-tech/mod_salesforce/system_settings
# github.com/metadiv-tech/mod_utils v1.2.12
## explicit; go 1.23.2
github.com/metadiv-tech/mod_utils
github.com/metadiv-tech/mod_utils/configs
github.com/metadiv-tech/mod_utils/endpoints/static
github.com/metadiv-tech/mod_utils/endpoints/system_setting
github.com/metadiv-tech/mod_utils/endpoints/system_smtp
github.com/metadiv-tech/mod_utils/entities
github.com/metadiv-tech/mod_utils/environments
github.com/metadiv-tech/mod_utils/errors
github.com/metadiv-tech/mod_utils/services/email_service
github.com/metadiv-tech/mod_utils/services/static_service
github.com/metadiv-tech/mod_utils/services/system_setting_service
github.com/metadiv-tech/mod_utils/system_settings
# github.com/metadiv-tech/mod_workflow v0.0.0-**************-5e3236bbd688
## explicit; go 1.23.2
github.com/metadiv-tech/mod_workflow
github.com/metadiv-tech/mod_workflow/actions
github.com/metadiv-tech/mod_workflow/endpoints/actions
github.com/metadiv-tech/mod_workflow/endpoints/workflow
github.com/metadiv-tech/mod_workflow/entities
github.com/metadiv-tech/mod_workflow/errors
github.com/metadiv-tech/mod_workflow/services/workflow_service
# github.com/metadiv-tech/nanoid v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/nanoid
# github.com/metadiv-tech/pwd v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/pwd
# github.com/metadiv-tech/rsa v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/rsa
# github.com/metadiv-tech/salesforce v0.0.0-20250625124304-1d4418e51bd7
## explicit; go 1.23.2
github.com/metadiv-tech/salesforce
# github.com/metadiv-tech/smtp_dialer v1.1.0
## explicit; go 1.23.2
github.com/metadiv-tech/smtp_dialer
# github.com/metadiv-tech/whatsapp_api v1.1.3
## explicit; go 1.23.2
github.com/metadiv-tech/whatsapp_api
# github.com/metadiv-tech/workflow v0.0.0-20250623073018-8436b455328e
## explicit; go 1.23.2
github.com/metadiv-tech/workflow
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/pelletier/go-toml/v2 v2.2.4
## explicit; go 1.21.0
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/robfig/cron v1.2.0
## explicit
github.com/robfig/cron
# github.com/robfig/go-cache v0.0.0-20130306151617-9fc39e0dbf62
## explicit
github.com/robfig/go-cache
# github.com/tkrajina/go-reflector v0.5.8
## explicit; go 1.17
github.com/tkrajina/go-reflector/reflector
# github.com/tkrajina/typescriptify-golang-structs v0.2.0
## explicit; go 1.16
github.com/tkrajina/typescriptify-golang-structs/typescriptify
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.3.0
## explicit; go 1.21
github.com/ugorji/go/codec
# github.com/ulule/limiter/v3 v3.11.2
## explicit; go 1.17
github.com/ulule/limiter/v3
github.com/ulule/limiter/v3/drivers/middleware/gin
github.com/ulule/limiter/v3/drivers/store/common
github.com/ulule/limiter/v3/drivers/store/memory
# golang.org/x/arch v0.18.0
## explicit; go 1.23.0
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.39.0
## explicit; go 1.23.0
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blowfish
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/sha3
# golang.org/x/net v0.41.0
## explicit; go 1.23.0
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
# golang.org/x/sync v0.15.0
## explicit; go 1.23.0
golang.org/x/sync/semaphore
# golang.org/x/sys v0.33.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
# golang.org/x/text v0.26.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
# gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc
## explicit
gopkg.in/alexcesaro/quotedprintable.v3
# gopkg.in/mail.v2 v2.3.1
## explicit
gopkg.in/mail.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/driver/mysql v1.6.0
## explicit; go 1.18
gorm.io/driver/mysql
# gorm.io/driver/postgres v1.6.0
## explicit; go 1.20
gorm.io/driver/postgres
# gorm.io/driver/sqlite v1.6.0
## explicit; go 1.20
gorm.io/driver/sqlite
# gorm.io/gorm v1.30.0
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/internal/lru
gorm.io/gorm/internal/stmt_store
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
